# Comprehensive Cost Analysis System for Generic-v3 Endpoint

## Overview

This document provides complete documentation for the comprehensive cost analysis system implemented for the `/generic-v3` endpoint. The system tracks all AI model usage, calculates costs in real-time, and provides detailed logging for every request.

## Features

### ✅ Real-time Cost Calculation
- Automatic token counting for all AI models
- Precise cost calculation using current model pricing
- Support for cached response cost savings (50% discount on input tokens)

### ✅ Multi-Model Support
- **OpenAI Models**: GPT-4o, GPT-4o-mini, GPT-4.1-mini, o1-mini
- **Claude Models**: Claude-3.5-Sonnet
- **Perplexity Models**: Sonar
- **Web Search APIs**: Cost tracking for external API calls

### ✅ Flow-Based Analysis
- Different processing flows are tracked separately
- Flow types: `generic_chat`, `deep_search`, `image_analysis`, `chart_analysis`, `web_search`, `question_classification`

### ✅ Comprehensive Logging
- Detailed cost breakdown per request
- Model-specific usage statistics
- Function-level cost tracking
- Request duration and performance metrics

## Model Pricing Configuration

```python
MODEL_PRICING = {
    "claude-3-5-sonnet-20241022": {"input": 3.0 / 1_000_000, "output": 15.0 / 1_000_000},
    "sonar": {"input": 1.0 / 1_000_000, "output": 1.0 / 1_000_000},
    "gpt-4o": {"input": 2.5 / 1_000_000, "output": 10.0 / 1_000_000},
    "gpt-4o-mini": {"input": 0.15 / 1_000_000, "output": 0.60 / 1_000_000},
    "gpt-4.1-mini": {"input": 0.40 / 1_000_000, "output": 1.60 / 1_000_000},
    "o1-mini": {"input": 3.0 / 1_000_000, "output": 12.0 / 1_000_000},
}
```

## Architecture

### Core Components

1. **ComprehensiveCostTracker**: Main tracking class
2. **ModelUsage**: Data class for individual model calls
3. **RequestCostAnalysis**: Data class for complete request analysis
4. **Helper Functions**: For tracking different API types

### Integration Points

The cost tracking system is integrated at multiple levels:

#### 1. Endpoint Level (`app/api/v3/endpoints/generic_bot.py`)
- Request initialization and finalization
- Flow type setting
- Streaming response wrapper with cost tracking

#### 2. Service Level
- **Generic Chat Service** (`app/services/generic_chat_service.py`)
- **Deep Search Service** (`app/services/deep_search_service.py`)
- **Web Search Service** (`app/services/web_search_services.py`)

#### 3. Utility Level
- **Generic Utils** (`app/utils/generic_utils.py`)
- **Deep Search Utils** (`app/utils/deep_search.py`)
- **Claude FMP Service** (`app/services/claude_fmp_service.py`)

## Tracked Functions

### Question Classification & Processing
- `classify_or_answer_question()` - GPT-4o-mini
- `classify_or_answer_question_v3()` - GPT-4o-mini
- `trade_or_answer_question()` - GPT-4o-mini
- `trade_or_answer_question_v3()` - GPT-4o-mini
- `check_question_from_history_v3()` - GPT-4o-mini

### Deep Search Functions
- `analyze_and_generate_sub_questions_v3()` - GPT-4o-mini (high reasoning)
- `fetch_subq_sonar_answer()` - Perplexity Sonar
- `synthesize_findings()` - GPT-4o-mini (high reasoning)
- `research_sub_question()` - o1-mini (high reasoning)

### Web Search Functions
- `web_scrape_search_v3()` - Perplexity Sonar + Web Search API
- Web search API calls tracked separately

### Image Analysis Functions
- `call_llm_image_analysis()` - GPT-4o (vision)
- Chart analysis and technical analysis

### Summary Generation
- `stream_summary_answer()` - Claude-3.5-Sonnet
- `summary_stream_handler()` - Claude-3.5-Sonnet

### Claude-based Functions
- `ask_claude()` - Claude-3.5-Sonnet
- `classify_query()` - GPT-4o-mini

## Usage

### Automatic Integration

The cost tracking system is automatically activated for every request to the `/generic-v3` endpoint. No additional code changes are required.

### Request Flow

1. **Request Start**: Cost tracker initializes with request details
2. **Flow Detection**: System identifies the processing flow type
3. **Model Calls**: Each AI model call is automatically tracked
4. **Cost Calculation**: Real-time cost calculation with token counting
5. **Request End**: Comprehensive cost analysis is logged

### Example Request Tracking

```python
# Automatic initialization in endpoint
request_id = cost_tracker.start_request_tracking(
    endpoint="/generic-v3",
    question=data.question,
    customer_id=customer_id,
    account_id=data.account_id,
    chat_id=chat_id
)

# Flow type setting
cost_tracker.set_flow_type("deep_search")

# Automatic model call tracking (happens in background)
# ... model calls throughout the request ...

# Automatic finalization
cost_tracker.finalize_request_tracking(request_duration_ms)
```