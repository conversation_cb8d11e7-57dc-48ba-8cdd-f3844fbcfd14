// Dashboard JavaScript functions for cost analysis visualization

function updateCharts(stats) {
    updateModelCostChart(stats.top_models);
    updateFlowChart(stats.top_flows);
    updateHourlyChart(stats.cost_by_hour);
}

function updateModelCost<PERSON>hart(topModels) {
    const ctx = document.getElementById('modelCostChart').getContext('2d');

    if (charts.modelCostChart) {
        charts.modelCostChart.destroy();
    }

    const colors = [
        '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
        '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
    ];

    charts.modelCostChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: topModels.map(m => m.model),
            datasets: [{
                data: topModels.map(m => m.cost),
                backgroundColor: colors.slice(0, topModels.length),
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const model = topModels[context.dataIndex];
                            return `${context.label}: $${model.cost.toFixed(4)} (${model.calls} calls)`;
                        }
                    }
                }
            }
        }
    });
}

function updateFlowChart(topFlows) {
    const ctx = document.getElementById('flowChart').getContext('2d');

    if (charts.flowChart) {
        charts.flowChart.destroy();
    }

    charts.flowChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: topFlows.map(f => f.flow),
            datasets: [{
                label: 'Requests',
                data: topFlows.map(f => f.requests),
                backgroundColor: '#36A2EB',
                borderColor: '#36A2EB',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const flow = topFlows[context.dataIndex];
                            return `${context.label}: ${flow.requests} requests ($${flow.cost.toFixed(4)})`;
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        precision: 0
                    }
                }
            }
        }
    });
}

function updateHourlyChart(costByHour) {
    const ctx = document.getElementById('hourlyChart').getContext('2d');

    if (charts.hourlyChart) {
        charts.hourlyChart.destroy();
    }

    charts.hourlyChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: costByHour.map(h => {
                const date = new Date(h.hour);
                return date.toLocaleDateString() + ' ' + date.getHours() + ':00';
            }),
            datasets: [{
                label: 'Cost ($)',
                data: costByHour.map(h => h.cost),
                borderColor: '#FF6384',
                backgroundColor: 'rgba(255, 99, 132, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.4
            }, {
                label: 'Requests',
                data: costByHour.map(h => h.requests),
                borderColor: '#36A2EB',
                backgroundColor: 'rgba(54, 162, 235, 0.1)',
                borderWidth: 2,
                fill: false,
                yAxisID: 'y1'
            }]
        },
        options: {
            responsive: true,
            interaction: {
                mode: 'index',
                intersect: false,
            },
            plugins: {
                legend: {
                    position: 'top',
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            if (context.datasetIndex === 0) {
                                return `Cost: $${context.parsed.y.toFixed(4)}`;
                            } else {
                                return `Requests: ${context.parsed.y}`;
                            }
                        }
                    }
                }
            },
            scales: {
                x: {
                    display: true,
                    title: {
                        display: true,
                        text: 'Time'
                    },
                    ticks: {
                        maxTicksLimit: 12
                    }
                },
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: 'Cost ($)'
                    },
                    ticks: {
                        callback: function(value) {
                            return '$' + value.toFixed(4);
                        }
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: 'Requests'
                    },
                    grid: {
                        drawOnChartArea: false,
                    },
                    ticks: {
                        precision: 0
                    }
                }
            }
        }
    });
}

function updateRecentRequests(recentRequests) {
    const tbody = document.querySelector('#recentRequestsTable tbody');

    if (!recentRequests || recentRequests.length === 0) {
        tbody.innerHTML = '<tr><td colspan="8" class="loading">No recent requests found</td></tr>';
        return;
    }

    tbody.innerHTML = recentRequests.map(request => `
        <tr onclick="showRequestDetails('${request.request_id}')" style="cursor: pointer;">
            <td title="${request.request_id}">${request.request_id.substring(0, 8)}...</td>
            <td><span class="flow-badge">${request.flow_type}</span></td>
            <td title="${request.question}">${request.question}</td>
            <td>${request.models_used.join(', ')}</td>
            <td><span class="cost-badge">$${request.total_cost.toFixed(4)}</span></td>
            <td>${request.total_tokens.toLocaleString()}</td>
            <td>${Math.round(request.duration_ms)}ms</td>
            <td>${new Date(request.timestamp).toLocaleTimeString()}</td>
        </tr>
    `).join('');
}

function showRequestDetails(requestId) {
    // This could open a modal or navigate to a detailed view
    console.log('Show details for request:', requestId);

    // For now, just show an alert with basic info
    fetch(`/api/v1/cost-dashboard/request/${requestId}`)
        .then(response => response.json())
        .then(data => {
            const request = data.request;
            alert(`Request Details:

ID: ${request.request_id}
Flow: ${request.flow_type}
Cost: $${request.total_cost.toFixed(4)}
Tokens: ${request.total_tokens.toLocaleString()}
Duration: ${Math.round(request.request_duration_ms)}ms
Models: ${request.model_usages.map(u => u.model_name).join(', ')}
            `);
        })
        .catch(error => {
            console.error('Error fetching request details:', error);
            alert('Error loading request details');
        });
}