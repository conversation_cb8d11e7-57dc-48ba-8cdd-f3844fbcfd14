#!/usr/bin/env python3
"""
🚀 Simple script to start just the Streamlit dashboard
"""

import subprocess
import sys
import os
import webbrowser
import time

def main():
    print("📊 Starting Streamlit Cost Analysis Dashboard")
    print("=" * 50)

    # Check if required files exist
    if not os.path.exists("streamlit_dashboard.py"):
        print("❌ streamlit_dashboard.py not found!")
        return

    print("✅ streamlit_dashboard.py found")
    print("🚀 Starting Streamlit dashboard...")
    print("📊 Dashboard will be available at: http://localhost:8501")
    print("💡 Make sure your FastAPI server is running on port 8000")

    try:
        # Open browser after a delay
        def open_browser():
            time.sleep(3)
            try:
                webbrowser.open("http://localhost:8501")
            except:
                pass

        import threading
        browser_thread = threading.Thread(target=open_browser, daemon=True)
        browser_thread.start()

        # Start Streamlit
        subprocess.run([
            sys.executable, "-m", "streamlit", "run",
            "streamlit_dashboard.py",
            "--server.port", "8501",
            "--server.address", "0.0.0.0"
        ], cwd=os.getcwd())

    except KeyboardInterrupt:
        print("\n🛑 Dashboard stopped")
        print("👋 Goodbye!")

if __name__ == "__main__":
    main()