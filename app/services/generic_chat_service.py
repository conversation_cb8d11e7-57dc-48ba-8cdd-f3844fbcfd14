import asyncio
import base64
import functools
import json
import os
import re
import time
import traceback
import uuid
from datetime import datetime
from decimal import Decimal
from typing import Optional, Dict, Any, List

import requests
from fastapi import HTTPException, Request
from fastapi.background import BackgroundTasks
from fastapi.responses import J<PERSON>NResponse
from fastapi_pagination import Params
from fastapi_pagination.ext.sqlalchemy import paginate
from langchain.chains.llm import LL<PERSON>hain
from langchain.chains.question_answering import load_qa_chain
from langchain.prompts import PromptTemplate
from openai import OpenAI
from pydantic import BaseModel
from sqlalchemy import insert, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.crud.chat_crud import (
    create_chat,
    create_chat_record,
    get_chat_node,
    get_chat_pair_by_response_id,
    set_chat_as_active,
    update_chat,
    update_chat_branches
)
from app.models.models import ScanImage
from app.prompts.generic_prompts import (
    STREAM_SUMMARY_PROMPt,
    first_prompt,
    summary_prompt,
    trade_prompt,
)
from app.schemas.agent_schemas import ChatSchema
from app.schemas.chat_schemas import (
    ChartDataItem,
    ChatModelSchema,
    GenericChatSchema,
    SimplifiedChartRequest_chrome,
    SimplifiedChartRequest_web,
    WebChartAnalysisResult, SimplifiedChartRequest_webV2, ChartData,
)
from app.schemas.drawing_tool_schemas import DrawingToolChatSchema, AnalysisRequest
from app.services.chat_with_image_service import (
    chat_with_image_generic,
    chat_with_image_generic_v3,
    download_image,
    encode_image,
    remove_image,
)
from app.services.chat_with_websocket_data import execute
from app.services.claude_fmp_service import AsyncTopicClassifier, fmp_execute
from app.services.mail_service import send_alert_mail
from app.services.web_search_services import web_scrape_search, web_scrape_search_v3
from app.utils import cache
from app.utils.chat_faqs_utils import get_answer_based_on_faqs
from app.utils.drawing_tool_utils import get_antropi_openai_client, TechnicalAnalysisAgent
from app.utils.general_utils import cache_qa, get_cached_qa, get_reusable_response
from app.utils.generic_utils import (  # stream_response,
    build_metric_json_data,
    check_question_from_history,
    check_question_from_history_v3,
    classify_or_answer_question,
    classify_or_answer_question_v3,
    extract_json_content,
    metric_execute_query,
    prepare_dataset,
    summary_answer,
    summary_stream_handler,
    summary_stream_handler_v3,
    trade_or_answer_question,
    trade_or_answer_question_v3,
    websearch_stream_handler,
    websearch_stream_handler_v3,
)
from app.utils.llm_utils import get_openai_model, get_openai_model_v3
from app.utils.logger import generic_logger as logger
from app.utils.rds_agent_utils import rds_sql_agent
from app.utils.symbol_based_answer_generating import create_memory_from_manual_history_dict
from app.utils.validate_user_query_request import validate_user_question

# Database configuration
DATABASE_SELECTOR = {
    "sql_query_db1": settings.DATABASE_1,
    "sql_query_db2": settings.DATABASE_1,
    "sql_query_db3": settings.DATABASE_1,
    "sql_query_db4": settings.DATABASE_1,
    "sql_query_db5": settings.DATABASE_5,
    "sql_query_db6": settings.DATABASE_1,
    "sql_query_db7": settings.DATABASE_1,
}


def log_execution_time(start_time, label):
    """Utility function to log execution time."""
    print(f"{label} took {time.time() - start_time:.2f} seconds")


def update_chat_history(history_key, history, question, answer):
    """Update and cache chat history."""
    history.append([question, answer])
    cache.set(history_key, history)


async def save_chat(chat_id, parent, question, answer, edited=False, **kwargs):
    """Save chat data to mongodb."""
    data_id = str(uuid.uuid4())
    chat_data = {
        "id": data_id,
        "chat_id": chat_id,
        "parent": parent,
        "question": question,
        "answer": answer,
        **kwargs,
    }
    if cache.get(f"chat_context_saved_{chat_id}") is not None:
        chat_data["file_paths"] = cache.get(f"chat_context_{chat_id}").get("file_paths")
        cache.delete(f"chat_context_saved_{chat_id}")
        # remove file_paths and set it
        data = cache.get(f"chat_context_{chat_id}")
        if data:
            data.pop("file_paths", None)
            cache.set(f"chat_context_{chat_id}", data)
    if edited:
        parallel_chat = await get_chat_node(id=parent)
        chat_data["parent"] = parallel_chat.get("parent")
        await create_chat(chat_data)
        parent_chat = await get_chat_node(id=parallel_chat.get("parent"))
        children = parent_chat.get("children", [])
        children.append(data_id)
        active = len(children) - 1
        await update_chat(id=parent_chat.get("id"), children=children, active=active)
    else:
        await create_chat(chat_data)
        chat = await get_chat_node(id=parent)
        children = chat.get("children", [])
        children.append(data_id)
        await update_chat(id=parent, children=children)
    return {
        "id": data_id,
        "chat_id": chat_id,
    }

async def process_sql_query(db_key, query, account_id, db_selector):
    if db_key == "sql_query_db1":
        # Validate account_id before using it in the query
        safe_account_id = validate_account_id(account_id)
        cte = f"WITH user_trades_{safe_account_id} AS (SELECT * FROM trades WHERE account = :account_id) "
        query = cte + query.replace(
            "vuetra_trading.trades", f"user_trades_{safe_account_id}"
        )  # Prepend the CTE to the main query
        # query = cte + query  # Prepend the CTE to the main query

    # Avoid logging full query for security reasons
    logger.info("Executing updated query safely.")

    db_uri = db_selector.get(db_key)
    params = {"account_id": account_id}  # Bind only the values, not table names
    db_result = metric_execute_query(query, db_uri, params)

    return build_metric_json_data(db_result)


def validate_account_id(account_id):
    """Ensure account_id contains only safe characters to prevent SQL injection."""
    if not re.match(r"^[a-zA-Z0-9_]+$", str(account_id)):
        raise ValueError(
            "Invalid account_id format detected. Possible SQL injection attempt."
        )
    return account_id


async def classify_question(user_question, prompt):  # history,
    """Classify the question and return initial results."""
    start_time = time.time()
    result = classify_or_answer_question(user_question, prompt)  # history[-3:],
    log_execution_time(start_time, "Question classification")
    return result


async def classify_question_v3(user_question, prompt):  # history,
    """Classify the question and return initial results."""
    start_time = time.time()
    result = classify_or_answer_question_v3(user_question, prompt)  # history[-3:],
    log_execution_time(start_time, "Question classification")
    return result


async def trade_question(user_question, prompt):  # history,
    """Classify the question and return initial results."""
    start_time = time.time()
    result = trade_or_answer_question(
        user_question, prompt
    )  # history,(user_question, prompt)    # history[-3:],
    log_execution_time(start_time, "Question classification")
    return result


async def trade_question_v3(user_question, prompt):  # history,
    """Classify the question and return initial results."""
    start_time = time.time()
    result = trade_or_answer_question_v3(
        user_question, prompt
    )  # history,(user_question, prompt)    # history[-3:],
    log_execution_time(start_time, "Question classification")
    return result


async def generate_summary(user_question, result_db, history, prompt):
    """Generate the final answer using a summary prompt."""
    start_time = time.time()
    final_result = summary_answer(user_question, result_db, history[-3:], prompt)
    final_result = re.sub(r"`|json|\n|\\", "", final_result)
    log_execution_time(start_time, "Summary generation")
    return json.loads(final_result)


async def check_response_relevance(user_question, generic_chat_history, response):
    classifier = AsyncTopicClassifier(settings.ANTHROPIC_API_KEY)
    messages = []
    for message in generic_chat_history[-3:]:
        messages.append({"role": "user", "content": message[0]})
        messages.append({"role": "assistant", "content": message[1]})
    summary_prompt = f"if user's question is: {user_question} and the response is: {response}. if the response has valid answer to the question or the question is about history of the chat without any apologies  return 1 else 0. No explanation or comment needed. history: {messages}"
    answer = await classifier.ask_claude(summary_prompt)
    if answer == "0":
        # response = await web_scrape_search(user_question, flag=True)
        response = await web_scrape_search(user_question)
        return response
    return answer


def log_function_call(func):
    """Decorator to log function calls."""

    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        logger.info(
            f"Calling function: {func.__name__} with args: {args} and kwargs: {kwargs}"
        )
        result = await func(*args, **kwargs)
        logger.info(f"Function {func.__name__} completed execution")
        return result

    return wrapper


@log_function_call
async def generic_chat_service(
    db: AsyncSession,
    chat_id: str,
    account_id: int,
    trading_token: str,
    generic_chat_history: list = [],
    is_websearch_enabled: bool = False,
    question: Optional[str] = None,
    reply: Optional[str] = None,
    token: str = "",
    track: str = "",
    selected_category: Optional[str] = None,
    selected_option: Optional[str] = None,
    background_tasks: BackgroundTasks = None,
    parent: str = "",
    edited_question: bool = False,
):
    history_key = f"chat_history_{chat_id}"
    reusable_response = get_reusable_response(prompt=question)
    logger.info(
        f"chat_id: {chat_id}, parent: {parent}, question: {question}, reply: {reply}"
    )
    if reusable_response is not None:
        save_chat_data = await save_chat(
            chat_id,
            parent,
            question=question,
            answer=reusable_response,
            edited=edited_question,
            reply=reply,
        )
        update_chat_history(
            history_key, generic_chat_history, question, reusable_response
        )
        result = {
            "answer": reusable_response,
            "chat_id": save_chat_data.get("chat_id"),
            "id": save_chat_data.get("id"),
        }
        return JSONResponse(result)  # Early return to avoid AttributeError
    cached_answer = get_cached_qa(question)
    if cached_answer is not None:
        save_chat_data = await save_chat(
            chat_id,
            parent,
            question=question,
            answer=cached_answer,
            edited=edited_question,
            reply=reply,
        )
        update_chat_history(history_key, generic_chat_history, question, cached_answer)
        result = {
            "answer": cached_answer,
            "chat_id": save_chat_data.get("chat_id"),
            "id": save_chat_data.get("id"),
        }
        return JSONResponse(result)

    user_question = f"Reply to: {reply}\n\nQuestion: {question}" if reply else question
    if is_websearch_enabled:
        user_question += " Please search the internet for the answer."

    logger.info(f"User question: {user_question}")
    previous_question_check = check_question_from_history(
        user_question, generic_chat_history[-3:]
    )

    try:
        test_question = json.loads(previous_question_check)
    except Exception as e:
        test_question = {"response": "Null", "is_new_question": previous_question_check}

    if test_question.get("response") != "Null":
        result = {"answer": test_question["response"], "chat_id": chat_id}
        # await save_chat_to_db(chat_id, question, reply, test_question["response"], response_id, chat_type="NORMAL", question_mode="generic")
        chat_data = await save_chat(
            chat_id,
            parent,
            question=question,
            answer=test_question["response"],
            edited=edited_question,
            reply=reply,
        )
        update_chat_history(
            history_key, generic_chat_history, question, test_question["response"]
        )
        result["id"] = chat_data.get("id")
        result["chat_id"] = chat_data.get("chat_id")
        # cache_qa(question, test_question["response"])
        return JSONResponse(result)  # Early return to avoid AttributeError
        # test_question = {"response": "Null", "is_new_question": previous_question_check, "answer": "Null", "external_answer": "Null"}

    # If is_new_question exists, use it as user_question
    if test_question.get("is_new_question") != "Null":
        user_question = test_question["is_new_question"]
    else:
        user_question = question

    # Fetch account details
    try:
        query = f"SELECT token, customer FROM {settings.DATABASE_5.split('/')[-1]}.accounts WHERE id = :account_id"
        params = {"account_id": account_id}

        result = build_metric_json_data(
            metric_execute_query(query, settings.DATABASE_5, params)
        )

        account_id = result[0]["token"]
        customer_id = str(result[0]["customer"])
    except Exception as e:
        logger.error(f"Error fetching account data: {e}")
        customer_id = account_id = ""

    decoded_customer_id = track.split("_")[0]
    logger.info(
        f"Customer ID ====>: {customer_id}, Decoded ====>: {decoded_customer_id}, Account ID ====>: {account_id}"
    )

    if selected_category:
        answer = await web_scrape_search(user_question)
        update_chat_history(
            history_key, generic_chat_history, user_question, answer["answer"]
        )
        result = {
            "answer": answer["answer"],
            "chat_id": chat_id,
            "source": answer["sources"],
        }
        # await save_chat_to_db(chat_id, question,reply, result["answer"], response_id, chat_type="NORMAL", question_mode="generic", source=answer["source"])
        chat_data = await save_chat(
            chat_id,
            parent,
            question=question,
            answer=answer["response"],
            edited=edited_question,
            reply=reply,
            source=answer["source"],
        )
        result["id"] = chat_data.get("id")
        result["chat_id"] = chat_data.get("chat_id")
        cache_qa(question, answer["response"], ex=60 * 60 * 2)
        return JSONResponse(result)  # Early return to avoid AttributeError

    system_prompt = (
        f"- Note: Ensure using the user's ID in queries where possible. Unique ID: {account_id}"
        + first_prompt
    )

    logger.info("Calling classify_question function")
    if test_question.get("external_answer") != "Null":
        first_result = await classify_question(user_question, system_prompt)
    else:
        first_result = await classify_question(user_question, system_prompt)
    logger.info("classify_question function executed successfully")

    cleaned_result = first_result.replace("`", "").replace("json", "").replace("\n", "")
    json_content = extract_json_content(cleaned_result)
    if background_tasks:
        background_tasks.add_task(prepare_dataset, user_question, json_content)
    logger.info(f"json_content: {json_content}")

    result = {}

    if json_content is None:
        update_chat_history(
            history_key, generic_chat_history, user_question, cleaned_result
        )
        result = {"answer": cleaned_result, "chat_id": chat_id}
        # await save_chat_to_db(chat_id, question,reply, result["answer"], response_id, chat_type="NORMAL", question_mode="generic")
        chat_data = await save_chat(
            chat_id,
            parent,
            question=question,
            answer=result["answer"],
            edited=edited_question,
            reply=reply,
        )
        result["id"] = chat_data.get("id")
        result["chat_id"] = chat_data.get("chat_id")
        cache_qa(question, result["answer"], ex=60 * 60 * 24 * 30)
        return JSONResponse(result)  # Early return to avoid AttributeError

    # Step 2: Handle specific JSON content cases
    if json_content and "answer" in json_content:
        update_chat_history(
            history_key, generic_chat_history, user_question, json_content["answer"]
        )
        result = {"answer": json_content["answer"], "chat_id": chat_id}
        # await save_chat_to_db(chat_id, question,reply, result["answer"], response_id, chat_type="NORMAL", question_mode="generic")
        chat_data = await save_chat(
            chat_id,
            parent,
            question=question,
            answer=result["answer"],
            edited=edited_question,
            reply=reply,
        )
        result["id"] = chat_data.get("id")
        result["chat_id"] = chat_data.get("chat_id")
        cache_qa(question, result["answer"], ex=60 * 60 * 24 * 30)
        return JSONResponse(result)  # Early return to avoid AttributeError

    if json_content:
        sql_query_db5 = json_content.get("sql_query_db5")
        if sql_query_db5 and sql_query_db5.lower() == "websocket":
            answer = execute(trading_token, user_question)
            update_chat_history(
                history_key, generic_chat_history, user_question, answer
            )
            result = {"answer": answer, "chat_id": chat_id}

        sql_query_db1 = json_content.get("sql_query_db1")
        if sql_query_db1 and sql_query_db1.lower() == "trade":
            answer = await trade_question(user_question, trade_prompt)
            cleaned_result = (
                answer.replace("`", "").replace("json", "").replace("\n", "")
            )
            json_decoded_content = extract_json_content(cleaned_result)
            json_content["sql_query_db1"] = json_decoded_content.get("sql_query_db1")
            json_content["chart_type"] = json_decoded_content.get("chart_type")
            if background_tasks:
                background_tasks.add_task(prepare_dataset, user_question, json_content)

        sql_query_db8 = json_content.get("sql_query_db8")
        if sql_query_db8 and sql_query_db8.lower() == "fmpdata":
            answer = await fmp_execute(user_question, chat_id)
            update_chat_history(
                history_key, generic_chat_history, user_question, answer["summary"]
            )
            if "chart_data" in answer.keys():
                result = {
                    "answer": answer["summary"],
                    "chat_id": chat_id,
                    "chart_data": answer["chart_data"],
                }
            else:
                result = {"answer": answer["summary"], "chat_id": chat_id}

        sql_query_db9 = json_content.get("sql_query_db9")
        if sql_query_db9 and sql_query_db9.lower() == "websearch":
            answer = await web_scrape_search(user_question)
            update_chat_history(
                history_key, generic_chat_history, user_question, answer["answer"]
            )
            result = {
                "answer": answer["answer"],
                "chat_id": chat_id,
                "source": answer["sources"],
            }

        sql_query_db10 = json_content.get("sql_query_db10")
        if sql_query_db10 and sql_query_db10.lower() == "economicindicatordata":
            answer = await rds_sql_agent(user_question, "economicindicatordata", track)
            update_chat_history(
                history_key, generic_chat_history, user_question, answer["output"]
            )
            result = {"answer": answer["output"], "chat_id": chat_id}

        if result:  # nned to update if result is null here
            answer = await check_response_relevance(
                user_question, generic_chat_history, result["answer"]
            )
            if type(answer) == dict and "sources" in answer.keys():
                result = {
                    "answer": answer["answer"],
                    "chat_id": chat_id,
                    "source": answer["sources"],
                }
            else:
                result["answer"] = result.get("answer")

            update_chat_history(
                history_key, generic_chat_history, user_question, result["answer"]
            )
            # await save_chat_to_db(chat_id, question,reply, result["answer"], response_id, chat_type="NORMAL", question_mode="generic")
            chat_data = await save_chat(
                chat_id,
                parent,
                question=question,
                answer=result["answer"],
                edited=edited_question,
                reply=reply,
            )
            result["id"] = chat_data.get("id")
            result["chat_id"] = chat_data.get("chat_id")
            cache_qa(question, result["answer"], ex=60 * 60 * 2)
            return result

    # Step 3: Process database queries
    result_db, news_ids, event_ids, metric_json = [], [], {}, None
    chart_type = json_content.pop("chart_type", None)

    if (
        json_content.get("sql_query_db1") != "null"
        and customer_id != decoded_customer_id
    ):
        logger.error("Unauthorized trade data access attempted")
        # await save_chat_to_db(chat_id, question,reply, "You don't have any trade data.", response_id, chat_type="NORMAL", question_mode="generic")
        chat_data = await save_chat(
            chat_id,
            parent,
            question=question,
            answer="You don't have any trade data.",
            edited=edited_question,
            reply=reply,
        )
        result = {
            "question": question,
            "answer": "You don't have any trade data.",
            "id": chat_data.get("id"),
            "chat_id": chat_data.get("chat_id"),
        }
        cache_qa(question, "You don't have any trade data.")
        return JSONResponse(
            content={
                "answer": "You don't have any trade data.",
                "chat_id": chat_id,
                "id": chat_data.get("id"),
            }
        )
    for db_key, query in json_content.items():
        if query and query != "null":
            logger.info(f"Processing SQL query for {db_key}")
            db_result = await process_sql_query(
                db_key, query, account_id, DATABASE_SELECTOR
            )
            logger.info(f"DB RESULT: {db_result}")
            result_db.append(db_result)

            if db_key == "sql_query_db2":
                for row in db_result:
                    event_date = row.get("date").split("T")[0]  # Extract YYYY-MM-DD
                    event_id = row.get("ID")

                    if event_date not in event_ids:
                        event_ids[
                            event_date
                        ] = []  # Initialize list if date is not present

                    event_ids[event_date].append(
                        event_id
                    )  # Append ID to the correct date

            elif db_key == "sql_query_db3":
                news_ids = [row.get("news_id") for row in db_result]

            if db_key in ["sql_query_db1", "sql_query_db7"]:
                metric_json = db_result

    # search flow start
    for db_key, query in json_content.items():
        if (
            (db_key == "sql_query_db2" and query != "null" and not event_ids)
            or (db_key == "sql_query_db3" and query != "null" and not news_ids)
            or (db_key == "sql_query_db4" and query != "null" and not db_result)
            or (db_key == "sql_query_db6" and query != "null" and not db_result)
            or (db_key == "sql_query_db7" and query != "null" and not db_result)
        ):
            # call web search
            answer = await web_scrape_search(user_question)
            update_chat_history(
                history_key, generic_chat_history, user_question, answer["answer"]
            )
            result = {
                "answer": answer["answer"],
                "chat_id": chat_id,
                "source": answer["sources"],
            }
            # await save_chat_to_db(chat_id, question,reply, result["answer"], response_id, chat_type="NORMAL", question_mode="generic")
            chat_data = await save_chat(
                chat_id,
                parent,
                question=question,
                answer=result["answer"],
                edited=edited_question,
                reply=reply,
            )
            result["id"] = chat_data.get("id")
            result["chat_id"] = chat_data.get("chat_id")
            if json_content.get("sql_query_db1").lower() != "null":
                cache_qa(question, result["answer"], ex=60 * 60 * 24 * 30)
            return result

    # Step 4: Generate summary and prepare response
    logger.info("Generating summary...")
    summary = await generate_summary(
        user_question, result_db, generic_chat_history, summary_prompt
    )
    logger.info(f"Summary: {summary}")

    update_chat_history(
        history_key, generic_chat_history, user_question, summary.get("answer")
    )

    response = {
        "symbol": summary.get("symbol"),
        "event_ids": event_ids,
        "news_ids": news_ids,
        "metric_data": metric_json,
        "chart_type": chart_type,
        "start_date": summary.get("start_date"),
        "end_date": summary.get("end_date"),
    }

    # await save_chat_to_db(chat_id, question, reply, summary["answer"], response_id, **response)
    chat_data = await save_chat(
        chat_id,
        parent,
        question=question,
        answer=summary["answer"],
        edited=edited_question,
        reply=reply,
        **response,
    )
    response["id"] = chat_data.get("id")
    response["chat_id"] = chat_data.get("chat_id")
    response.update({"answer": summary["answer"], "chat_id": chat_id})
    logger.info("Database updated successfully.")
    logger.info(f"Response: {response}")
    if json_content.get("sql_query_db1").lower() != "null":
        pass
    else:
        cache_qa(question, summary["answer"])
    return JSONResponse(response)


from app.rag.vector_database import ChromaDBManager
from app.utils.llm_utils import general_call


def handle_context_call(track, question, history, chat_id):
    chat_context_key = f"chat_context_{chat_id}"
    chat_context = cache.get(chat_context_key) or {}
    if history:
        conversation_string = "conversation:\n"
        for chat in history:
            conversation_string += f"Question: {chat[0]}\nAnswer: {chat[1]}\n\n"
    else:
        conversation_string = ""

    # comment for checking question needed to get answer based on documents
    # prompt = f"""
    # You are and ai agent, return yes or no for the following question needs context from the file mentioned or not.
    # {conversation_string}
    # question: {question}
    #
    # context_document_name: {chat_context.get('document_name')}
    #
    # response: 'yes'/'no'
    #
    # **note**:
    # - Always respond with 'yes'/'no' in the response.
    # - understand the question what user is asking about and analyze the situation and return 'yes'/'no'
    # ex:
    #  question: what does this doc say?
    #  answer: yes
    #  quesetion: what is this doc about?
    #  answer: yes
    #  question: summarize this doc
    #  answer: yes
    # """
    # resp = general_call(prompt)

    # if resp.strip().strip('"').lower() == "yes":

    # every request we can check answer from the documents if documents uploaded then
    cdb_manager = ChromaDBManager()
    cdb = cdb_manager.get_db(chat_id)
    chunks = cdb.similarity_search(question)
    context = "\n".join([chunk.page_content for chunk in chunks])
    prompt = f"""
    You are and ai agent, provide answer for the user question based on the context provided.
    {conversation_string}
    question: {question}

    context: {context}

    response: <answer>

    **note**:
    - Always respond with pure html format using html tags like <p>, <ul>, <li>. response should be directly rendered in UI with innerHTML.
    """
    resp = general_call(prompt)
    return resp.replace("```html", "").replace("```", "")
    # else:
    #     return ""


def handle_context_call_with_chain(track, question, history, chat_id):
    chat_context_key = f"chat_context_{chat_id}"
    chat_context = cache.get(chat_context_key) or {}

    if history:
        conversation_string = "conversation:\n"
        for chat in history:
            conversation_string += f"Question: {chat[0]}\nAnswer: {chat[1]}\n\n"
    else:
        conversation_string = ""

    cdb_manager = ChromaDBManager()
    vector_store = cdb_manager.get_db(chat_id)

    DEFAULT_PROMPT_TEMPLATE = """
        You are and financial ai agent, provide answer for the user question based on the context provided.
        {conversation_string}
        question: {question}

        context: {context}

        response: <answer>
        
        **important note**
        follow this response if below any points satisfied response: <p>intent_fallback_intent</p>
        1. if answer is not present and not relevant to given document
        2. user question relevant to market and event data
        3. if any time based information like month, today, tomorrow, yesterday, event specific mentioned into user question

        **note**:
        - Always respond with pure html format using html tags like <p>, <ul>, <li>. response should be directly rendered in UI with innerHTML.
        """

    custom_prompt_template = PromptTemplate(
        input_variables=["context", "conversation_string", "question"],
        template=DEFAULT_PROMPT_TEMPLATE,
    )

    # Get relevant documents
    docs = vector_store.similarity_search(question)

    # Create the stuff chain with our custom prompt
    qa_chain = load_qa_chain(
        llm=get_openai_model(), chain_type="stuff", prompt=custom_prompt_template
    )

    # Call the chain with all required inputs
    result = qa_chain(
        {
            "input_documents": docs,
            "question": question,
            "conversation_string": "",
            "context": "\n\n".join([doc.page_content for doc in docs]),
        }
    )

    resp = result.get(
        "output_text", result.get("answer", result.get("result", "No answer generated"))
    )
    if "intent_fallback_intent" in resp:
        return ""
    else:
        return resp.replace("```html", "").replace("```", "")
    # else:
    #     return ""


async def get_chart_data_from_lens_id(
    lens_id, track, db, params, chart_context, chat_id
):
    try:
        stmt = (
            select(ScanImage)
            .where(ScanImage.account == track, ScanImage.type == "web")
            .order_by(ScanImage.created_at.desc())
        )
        logger.info(f"query was: {stmt}")
        paginated_result = await paginate(db, stmt, params)
        logger.info(f"pagination result: {paginated_result}")

        try:
            for item in paginated_result.items:
                try:
                    item_lens_id = item.lens_id or ""
                    if item_lens_id and item_lens_id == lens_id:
                        updated_chart_data = json.loads(item.chartdata)
                        updated_chart_data = updated_chart_data["chartData"]
                        if chart_context:
                            pass
                        else:
                            cache.set(
                                f"cache_chart_data_{chat_id}",
                                {"chartdata": updated_chart_data},
                            )
                            chart_context = {"chartdata": updated_chart_data}

                except Exception as e:
                    print(f"Error in {e}")

            return chart_context

        except Exception as e:
            logger.error(f"Error coming when get chart data from lens_id: {e}")

    except Exception as e:
        logger.error(f"Error coming when get chart data from lens_id: {e}")

async def get_chart_data_based_on_lens_id(
    db: AsyncSession,
    lens_id: str,
    track: str = ""
):
    try:
        stmt = (
            select(ScanImage)
            .where(ScanImage.account == track, ScanImage.type == "web", ScanImage.lens_id == lens_id)
            .order_by(ScanImage.created_at.desc())
            .limit(1)  # Only need the most recent record
        )
        result = await db.execute(stmt)
        scan_image = result.scalar_one_or_none()

        if scan_image:
            try:
                updated_chart_data = json.loads(scan_image.chartdata)
                return updated_chart_data
            except Exception as e:
                logger.error(f"Error parsing chart data: {e}")
                return {}
        else:
            logger.info(f"No chart data found for lens_id: {lens_id}")
            return {}

    except Exception as e:
        logger.error(f"Error coming when get chart data based on lens_id: {e}")
        return {}

async def streaming_generic_chat_service(
    db: AsyncSession,
    chat_id: str,
    account_id: int,
    trading_token: str,
    params: Params,
    lens_id: str,
    generic_chat_history: list = [],
    is_websearch_enabled: bool = False,
    question: Optional[str] = None,
    reply: Optional[str] = None,
    token: str = "",
    track: str = "",
    selected_category: Optional[str] = None,
    selected_option: Optional[str] = None,
    background_tasks: BackgroundTasks = None,
    parent: str = "",
    edited_question: bool = False,
    original_question="",
    selections=None,
    file_paths=[],
):
    try:
        history_key = f"chat_history_{chat_id}"
        chat_context_key = f"chat_context_{chat_id}"
        chat_context = cache.get(chat_context_key) or {}
        chart_context = cache.get(f"cache_chart_data_{chat_id}") or {}
        if lens_id:
            chart_context = await get_chart_data_from_lens_id(
                lens_id, track, db, params, chart_context, chat_id
            )

        if chart_context:
            logger.info("coming into chart data functionality....")
            chartdata = chart_context.get("chartdata", [])
            resp = handle_chartdata_context_langchain(
                question, generic_chat_history, chat_id, chartdata, cache_store=False
            )
            if resp:
                save_chat_data = await save_chat(
                    chat_id,
                    parent,
                    question=original_question,
                    answer=resp,
                    edited=edited_question,
                    reply=reply,
                    selections=selections,
                    file_paths=file_paths,
                )
                update_chat_history(history_key, generic_chat_history, question, resp)
                result = {
                    "answer": resp,
                    "chat_id": save_chat_data.get("chat_id"),
                    "id": save_chat_data.get("id"),
                }
                resp = json.dumps(
                    {
                        "detail": result,
                        "type": "final",
                    }
                )
                yield f"data: {resp}\n\n"
                return

        if chat_context:
            resp = handle_context_call_with_chain(
                track, question, generic_chat_history, chat_id
            )
            if resp:
                save_chat_data = await save_chat(
                    chat_id,
                    parent,
                    question=original_question,
                    answer=resp,
                    edited=edited_question,
                    reply=reply,
                    selections=selections,
                    file_paths=file_paths,
                )
                update_chat_history(history_key, generic_chat_history, question, resp)
                result = {
                    "answer": resp,
                    "chat_id": save_chat_data.get("chat_id"),
                    "id": save_chat_data.get("id"),
                }
                resp = json.dumps(
                    {
                        "detail": result,
                        "type": "final",
                    }
                )
                yield f"data: {resp}\n\n"
                return

        reusable_response = get_reusable_response(prompt=question)
        if reusable_response is not None:
            save_chat_data = await save_chat(
                chat_id,
                parent,
                question=original_question,
                answer=reusable_response,
                edited=edited_question,
                reply=reply,
                selections=selections,
                file_paths=file_paths,
            )
            update_chat_history(
                history_key, generic_chat_history, question, reusable_response
            )
            result = {
                "answer": reusable_response,
                "chat_id": save_chat_data.get("chat_id"),
                "id": save_chat_data.get("id"),
            }
            resp = json.dumps(
                {
                    "detail": result,
                    "type": "final",
                }
            )
            yield f"data: {resp}\n\n"
            return
        cached_answer = get_cached_qa(question)
        if cached_answer is not None:
            save_chat_data = await save_chat(
                chat_id,
                parent,
                question=original_question,
                answer=cached_answer,
                edited=edited_question,
                reply=reply,
                selections=selections,
                file_paths=file_paths,
            )
            update_chat_history(
                history_key, generic_chat_history, question, cached_answer
            )
            result = {
                "answer": cached_answer,
                "chat_id": save_chat_data.get("chat_id"),
                "id": save_chat_data.get("id"),
            }
            resp = json.dumps(
                {
                    "detail": result,
                    "type": "final",
                }
            )
            yield f"data: {resp}\n\n"
            return
        # resp = json.dumps({"loading_message": "Please wait...", "type": "loading"})
        # yield f"data: {resp}\n\n"

        user_question = (
            f"Reply to: {reply}\n\nQuestion: {question}" if reply else question
        )
        if is_websearch_enabled:
            user_question += " Please search the internet for the answer."
        logger.info(f"User question: {user_question}")
        previous_question_check = check_question_from_history(
            user_question, generic_chat_history[-3:]
        )

        try:
            test_question = json.loads(previous_question_check)
        except Exception as e:
            test_question = {
                "response": "Null",
                "is_new_question": previous_question_check,
            }

        if test_question.get("response") != "Null":
            result = {"answer": test_question["response"], "chat_id": chat_id}
            # await save_chat_to_db(chat_id, question, reply, test_question["response"], response_id, chat_type="NORMAL", question_mode="generic")
            chat_data = await save_chat(
                chat_id,
                parent,
                question=original_question,
                answer=test_question["response"],
                edited=edited_question,
                reply=reply,
                selections=selections,
                file_paths=file_paths,
            )
            update_chat_history(
                history_key, generic_chat_history, question, test_question["response"]
            )
            result["id"] = chat_data.get("id")
            result["chat_id"] = chat_data.get("chat_id")
            resp = json.dumps(
                {
                    "detail": result,
                    "type": "final",
                }
            )
            yield f"data: {resp}\n\n"
            # cache_qa(question, test_question["response"])
            return

        # If is_new_question exists, use it as user_question
        if test_question.get("is_new_question") != "Null":
            user_question = test_question["is_new_question"]
        else:
            user_question = question

        # Fetch account details
        try:
            query = f"SELECT token, customer FROM {settings.DATABASE_5.split('/')[-1]}.accounts WHERE id = :account_id"
            params = {"account_id": account_id}

            result = build_metric_json_data(
                metric_execute_query(query, settings.DATABASE_5, params)
            )

            account_id = result[0]["token"]
            customer_id = str(result[0]["customer"])
        except Exception as e:
            logger.error(f"Error fetching account data: {e}")
            customer_id = account_id = ""

        decoded_customer_id = track.split("_")[0]
        logger.info(
            f"Customer ID ====>: {customer_id}, Decoded ====>: {decoded_customer_id}, Account ID ====>: {account_id}"
        )

        if selected_category:
            resp = json.dumps(
                {
                    "loading_message": f"Please wait, we're are analyzing data for {selected_category} in vuetra's database",
                    "type": "loading",
                }
            )
            yield f"data: {resp}\n\n"

            answer = ""
            try:
                async for chunk in websearch_stream_handler(user_question):
                    if chunk["type"] == "answer":
                        resp = json.dumps(
                            {"detail": {"answer": chunk["data"]}, "type": "data"}
                        )
                        yield f"data: {resp}\n\n"
                        answer += chunk["data"]
                    else:
                        resp = chunk.get("data", {})
                        break
            except StopIteration:
                pass

            update_chat_history(
                history_key, generic_chat_history, user_question, answer
            )

            result = {"answer": answer, "source": resp.get("sources")}

            chat_data = await save_chat(
                chat_id,
                parent,
                question=original_question,
                answer=answer,
                edited=edited_question,
                reply=reply,
                source=resp.get("sources"),
                selections=selections,
                file_paths=file_paths,
            )
            result["id"] = chat_data.get("id")
            result["chat_id"] = chat_data.get("chat_id")
            resp = json.dumps({"detail": result, "type": "final"})

            yield f"data: {resp}\n\n"
            cache_qa(question, answer, ex=60 * 60 * 2)
            return

        system_prompt = (
            f"- Note: Ensure using the user's ID in queries where possible. Unique ID: {account_id}"
            + first_prompt
        )

        logger.info("Calling classify_question function")
        if test_question.get("external_answer") != "Null":
            first_result = await classify_question(user_question, system_prompt)
        else:
            first_result = await classify_question(user_question, system_prompt)
        logger.info("classify_question function executed successfully")

        cleaned_result = (
            first_result.replace("`", "").replace("json", "").replace("\n", "")
        )
        json_content = extract_json_content(cleaned_result)
        logger.info(f"json_content: {json_content}")
        if background_tasks:
            background_tasks.add_task(prepare_dataset, user_question, json_content)
        logger.info(f"json_content: {json_content}")

        result = {}

        if json_content is None:
            update_chat_history(
                history_key, generic_chat_history, user_question, cleaned_result
            )
            result = {"answer": cleaned_result, "chat_id": chat_id}
            # await save_chat_to_db(chat_id, question,reply, result["answer"], response_id, chat_type="NORMAL", question_mode="generic")
            chat_data = await save_chat(
                chat_id,
                parent,
                question=original_question,
                answer=result["answer"],
                edited=edited_question,
                reply=reply,
                selections=selections,
                file_paths=file_paths,
            )
            result["id"] = chat_data.get("id")
            result["chat_id"] = chat_data.get("chat_id")
            resp = json.dumps(
                {
                    "detail": result,
                    "type": "final",
                }
            )
            yield f"data: {resp}\n\n"
            cache_qa(question, result["answer"], ex=60 * 60 * 24 * 30)
            return

        # Step 2: Handle specific JSON content cases
        if json_content and "answer" in json_content:
            update_chat_history(
                history_key, generic_chat_history, user_question, json_content["answer"]
            )
            result = {"answer": json_content["answer"], "chat_id": chat_id}
            # await save_chat_to_db(chat_id, question,reply, result["answer"], response_id, chat_type="NORMAL", question_mode="generic")
            chat_data = await save_chat(
                chat_id,
                parent,
                question=original_question,
                answer=result["answer"],
                edited=edited_question,
                reply=reply,
                selections=selections,
                file_paths=file_paths,
            )
            result["id"] = chat_data.get("id")
            result["chat_id"] = chat_data.get("chat_id")
            resp = json.dumps(
                {
                    "detail": result,
                    "type": "final",
                }
            )
            yield f"data: {resp}\n\n"
            cache_qa(question, result["answer"], ex=60 * 60 * 24 * 30)
            return

        if json_content:
            sql_query_db5 = json_content.get("sql_query_db5")
            if sql_query_db5 and sql_query_db5.lower() == "websocket":
                resp = json.dumps(
                    {"loading_message": "We're analyzing data", "type": "loading"}
                )
                yield f"data: {resp}\n\n"
                answer = execute(trading_token, user_question)
                update_chat_history(
                    history_key, generic_chat_history, user_question, answer
                )
                result = {"answer": answer, "chat_id": chat_id}

            sql_query_db1 = json_content.get("sql_query_db1")
            if sql_query_db1 and sql_query_db1.lower() == "trade":
                answer = await trade_question(user_question, trade_prompt)
                cleaned_result = (
                    answer.replace("`", "").replace("json", "").replace("\n", "")
                )
                json_decoded_content = extract_json_content(cleaned_result)
                json_content["sql_query_db1"] = json_decoded_content.get(
                    "sql_query_db1"
                )
                json_content["chart_type"] = json_decoded_content.get("chart_type")
                if background_tasks:
                    background_tasks.add_task(
                        prepare_dataset, user_question, json_content
                    )

            sql_query_db8 = json_content.get("sql_query_db8")
            if sql_query_db8 and sql_query_db8.lower() == "fmpdata":
                resp = json.dumps(
                    {
                        "loading_message": "We're analysing financial data",
                        "type": "loading",
                    }
                )
                yield f"data: {resp}\n\n"
                answer = await fmp_execute(user_question, chat_id)
                update_chat_history(
                    history_key, generic_chat_history, user_question, answer["summary"]
                )
                if "chart_data" in answer.keys():
                    result = {
                        "answer": answer["summary"],
                        "chat_id": chat_id,
                        "chart_data": answer["chart_data"],
                    }
                else:
                    result = {"answer": answer["summary"], "chat_id": chat_id}

            sql_query_db9 = json_content.get("sql_query_db9")
            if sql_query_db9 and sql_query_db9.lower() == "websearch":
                reps = json.dumps(
                    {"loading_message": "Searching the internet", "type": "loading"}
                )
                yield f"data: {reps}\n\n"
                answer = await web_scrape_search(user_question)
                update_chat_history(
                    history_key, generic_chat_history, user_question, answer["answer"]
                )
                result = {
                    "answer": answer["answer"],
                    "chat_id": chat_id,
                    "source": answer["sources"],
                }

            sql_query_db10 = json_content.get("sql_query_db10")
            if sql_query_db10 and sql_query_db10.lower() == "economicindicatordata":
                answer = await rds_sql_agent(
                    user_question, "economicindicatordata", track
                )
                update_chat_history(
                    history_key, generic_chat_history, user_question, answer["output"]
                )
                result = {"answer": answer["output"], "chat_id": chat_id}

            if result:  # nned to update if result is null here
                answer = await check_response_relevance(
                    user_question, generic_chat_history, result["answer"]
                )
                if type(answer) == dict and "sources" in answer.keys():
                    result = {
                        "answer": answer["answer"],
                        "chat_id": chat_id,
                        "source": answer["sources"],
                    }
                else:
                    result["answer"] = result.get("answer")

                update_chat_history(
                    history_key, generic_chat_history, user_question, result["answer"]
                )
                # await save_chat_to_db(chat_id, question,reply, result["answer"], response_id, chat_type="NORMAL", question_mode="generic")
                chat_data = await save_chat(
                    chat_id,
                    parent,
                    question=original_question,
                    answer=result["answer"],
                    edited=edited_question,
                    reply=reply,
                    selections=selections,
                    file_paths=file_paths,
                )
                result["id"] = chat_data.get("id")
                result["chat_id"] = chat_data.get("chat_id")
                resp = json.dumps(
                    {
                        "detail": result,
                        "type": "final",
                    }
                )
                yield f"data: {resp}\n\n"
                cache_qa(question, result["answer"], ex=60 * 60 * 2)
                return

        # Step 3: Process database queries
        result_db, event_ids, news_ids, metric_json = [], {}, [], None
        chart_type = json_content.pop("chart_type", None)

        if (
            json_content.get("sql_query_db1") != "null"
            and customer_id != decoded_customer_id
        ):
            logger.error("Unauthorized trade data access attempted")
            # await save_chat_to_db(chat_id, question,reply, "You don't have any trade data.", response_id, chat_type="NORMAL", question_mode="generic")
            chat_data = await save_chat(
                chat_id,
                parent,
                question=original_question,
                answer="You don't have any trade data.",
                edited=edited_question,
                reply=reply,
                selections=selections,
                file_paths=file_paths,
            )
            result = {"answer": "You don't have any trade data."}
            result["id"] = chat_data.get("id")
            result["chat_id"] = chat_data.get("chat_id")
            resp = json.dumps(
                {
                    "detail": result,
                    "type": "final",
                }
            )
            yield f"data: {resp}\n\n"
            cache_qa(question, "You don't have any trade data.")
            return
        loading_messages = {
            "sql_query_db1": "We are gathering your trade data.",
            "sql_query_db2": "We are gathering event data.",
            "sql_query_db3": "We are gathering news data.",
        }

        for db_key, query in json_content.items():
            if query and query != "null":
                logger.info(f"Processing SQL query for {db_key}")

                # Send loading message if applicable
                if db_key in loading_messages:
                    resp = json.dumps(
                        {"loading_message": loading_messages[db_key], "type": "loading"}
                    )
                    yield f"data: {resp}\n\n"

                db_result = await process_sql_query(
                    db_key, query, account_id, DATABASE_SELECTOR
                )
                logger.info(f"DB RESULT: {db_result}")
                result_db.append(db_result)

                if db_key == "sql_query_db2":
                    event_ids = {
                        row.get("date", "").split("T")[0]: [
                            row.get("ID") for row in db_result
                        ]
                        for row in db_result
                    }
                elif db_key == "sql_query_db3":
                    news_ids = [row.get("news_id") for row in db_result]
                elif db_key in {"sql_query_db1", "sql_query_db7"}:
                    metric_json = db_result

        # search flow start
        search_queries = {
            "sql_query_db2": event_ids,
            "sql_query_db3": news_ids,
            "sql_query_db4": db_result,
            "sql_query_db6": db_result,
            "sql_query_db7": db_result,
        }

        for db_key, query in json_content.items():
            if query != "null" and not search_queries.get(
                db_key, True
            ):  # Check if query is valid and relevant data is empty
                resp = json.dumps(
                    {
                        "loading_message": "We are searching the internet",
                        "type": "loading",
                    }
                )
                yield f"data: {resp}\n\n"
                answer = ""
                try:
                    async for chunk in websearch_stream_handler(user_question):
                        if chunk["type"] == "answer":
                            resp = json.dumps(
                                {"detail": {"answer": chunk["data"]}, "type": "data"}
                            )
                            yield f"data: {resp}\n\n"
                            answer += chunk["data"]
                        else:
                            resp = chunk.get("data", {})
                            break
                except StopIteration:
                    pass

                update_chat_history(
                    history_key, generic_chat_history, user_question, answer
                )

                result = {
                    "answer": answer,
                    "chat_id": chat_id,
                    "source": resp.get("sources"),
                }

                # await save_chat_to_db(chat_id, question, reply, answer_text, response_id,
                #                       chat_type="NORMAL", question_mode="generic")
                # await save_chat_to_db(chat_id, question, reply, answer["response"], response_id,
                #                       chat_type="NORMAL", question_mode="generic")
                chat_data = await save_chat(
                    chat_id,
                    parent,
                    question=original_question,
                    answer=answer,
                    edited=edited_question,
                    reply=reply,
                    selections=selections,
                    file_paths=file_paths,
                )
                result["id"] = chat_data.get("id")
                result["chat_id"] = chat_data.get("chat_id")
                resp = json.dumps({"detail": result, "type": "final"})
                yield f"data: {resp}\n\n"
                if json_content.get("sql_query_db1").lower() != "null":
                    cache_qa(question, answer["response"], ex=60 * 60 * 24 * 30)
                return

        # Step 4: Generate summary and prepare response
        logger.info("Generating summary...")
        resp = json.dumps({"loading_message": "Generating summary", "type": "loading"})
        yield f"data: {resp}\n\n"
        summary = ""
        end_stream_response = {}
        try:
            # summary = await generate_summary(user_question, result_db, generic_chat_history, STREAM_SUMMARY_PROMPt)
            async for chunk in summary_stream_handler(
                user_question=user_question,
                json_result=result_db,
                history=generic_chat_history,
                summary_prompt=STREAM_SUMMARY_PROMPt,
            ):
                if chunk.get("type") == "answer":
                    summary += chunk.get("data")
                    result = {"answer": chunk.get("data"), "chat_id": chat_id}
                    data_to_send = json.dumps({"detail": result, "type": "data"})
                    yield f"data: {data_to_send}\n\n"
                else:
                    end_stream_response = chunk.get("data", {})
        except StopIteration as e:
            logger.error(f"Stream ended")
            logger.info(f"Summary: {summary}")
        update_chat_history(history_key, generic_chat_history, user_question, summary)

        response = {
            "symbol": end_stream_response.get("symbol"),
            "event_ids": event_ids,
            "news_ids": news_ids,
            "metric_data": metric_json,
            "chart_type": chart_type,
            "start_date": end_stream_response.get("start_date"),
            "end_date": end_stream_response.get("end_date"),
        }
        # await save_chat_to_db(chat_id, question, reply, summary.get("answer"), response_id, **response)
        chat_data = await save_chat(
            chat_id,
            parent,
            question=original_question,
            answer=summary,
            edited=edited_question,
            reply=reply,
            selections=selections,
            file_paths=file_paths,
            **response,
        )
        logger.info("Database updated successfully.")
        logger.info(f"Response: {response}")
        response.update(
            {
                "answer": summary,
                "id": chat_data.get("id"),
                "chat_id": chat_data.get("chat_id"),
            }
        )

        rep = json.dumps({"detail": response, "type": "final"})
        yield f"data: {rep}\n\n"
        if json_content.get("sql_query_db1").lower() != "null":
            pass
        else:
            cache_qa(question, summary)
        return
    except Exception as e:
        logger.error(f"Error: {e}")
        send_alert_mail(f"Error: {e} \n {traceback.format_exc()}")
        yield f"data: {json.dumps({'error': 'Internal server error', 'type': 'error'})}\n\n"
        return


async def handle_chat_branching(db, data, track, chat_id):
    """Handles branching logic for edited questions."""
    if not data.edited_question:
        return chat_id

    logger.info(f"Processing edited question for chat_id: {chat_id}")
    new_branched_chat_id = str(uuid.uuid4())

    # Create new chat record as a branch
    await create_chat_record(
        db,
        ChatModelSchema(
            account_id=track,
            chat_id=new_branched_chat_id,
            chat_type="NORMAL",
            parent=chat_id,
            active=True,
            name="",
        ),
    )
    await set_chat_as_active(db, new_branched_chat_id)

    # Update chat history with the new branch
    edited_record = await get_chat_pair_by_response_id(
        chat_id=chat_id, response_id=data.response_id
    )

    if edited_record is None:
        logger.warning(
            f"No chat pair found for chat_id: {chat_id} and response_id: {data.response_id}"
        )
        return new_branched_chat_id  # Or return chat_id if you want to skip branching

    # Ensure 'branches' key exists and is a list
    edited_record.setdefault("branches", [])
    edited_record["branches"].append(new_branched_chat_id)

    await update_chat_branches(chat_id, data.response_id, edited_record["branches"])

    return new_branched_chat_id


async def handle_image_chat(
    data: GenericChatSchema, user_question, track, history, chat_id, db: AsyncSession
):
    """Handles chat requests that include images and fetches real-time data if needed."""
    logger.info(f"Running handle image chat for chat_id: {chat_id}")
    images = data.hidden_images or data.images or data.chat_images
    if not images:
        return None

    response = chat_with_image_generic(images, user_question, track, history=history)
    response_id = str(uuid.uuid4())

    # Extract the answer text without HTML to check if it's "realtime"
    answer_text = re.sub(r"<.*?>", "", response).strip()

    if answer_text.lower() == "realtime":
        # Fetch real-time data
        # realtime_data = await web_scrape_search(user_question or "", flag=True)
        realtime_data = await web_scrape_search(user_question or "")

        # Format the response with clickable links
        response_text = realtime_data.get("answer", "Real-time data unavailable.")
        sources = realtime_data.get("sources", [])

        # Add formatted source links at the end of the response
        if sources:
            response_text += "\n\nSources:\n"
            for i, source in enumerate(sources, 1):
                if isinstance(source, str) and source.startswith(
                    ("http://", "https://")
                ):
                    response_text += f"[Read more]({source})"
                    if i < len(sources):
                        response_text += " | "

        final_response = response_text
    else:
        final_response = response

    # Store response in history and cache
    history.append([user_question, final_response])
    cache.set(f"chat_history_{chat_id}", history)

    chat_data = await save_chat(
        chat_id,
        data.parent,
        question=data.question,
        answer=final_response,
        edited=data.edited_question,
        reply=data.reply,
        images=data.images,
        chat_images=data.chat_images,
        chat_full_images=data.hidden_images,
        logo=data.logo,
        symbol=data.symbol,
        timeframe=data.timeframe,
    )
    logger.info(f"Returning response: {chat_id}")
    return {
        "answer": final_response,
        "images": data.images,
        "id": chat_data.get("id"),
        "chat_id": chat_data.get("chat_id"),
    }


def encode_image(image_path):
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode("utf-8")


def remove_image(image_path):
    if os.path.exists(image_path):
        os.remove(image_path)


def download_image(image_url: str, account_id: int):
    image_response = requests.get(image_url, timeout=30)
    if image_response.status_code == 200:
        extension = image_url.split(".")[-1].lower()
        if extension in ["png", "jpg", "jpeg"]:
            file_name = f"{account_id}_image.{extension}"
            file_path = os.path.join(settings.TMP_DIR, file_name)
            os.makedirs(settings.TMP_DIR, exist_ok=True)
            with open(file_path, "wb") as image_file:
                image_file.write(image_response.content)
            return file_path, extension
        else:
            logger.error(f"Unsupported image format: {extension}")
            raise Exception(f"Unsupported image format: {extension}")
    else:
        logger.error(f"Failed to download image: {image_response.status_code}")
        raise Exception(f"Failed to download image: {image_response.status_code}")


client = OpenAI(api_key=settings.OPENAI_API_KEY)


def convert_chartdata_to_dict_v1(chart_data_list):
    """Convert ChartDataItem objects to list of dictionaries using Pydantic's model_dump()"""
    return [item.model_dump() for item in chart_data_list]


def handle_chartdata_context_langchain(
    question, history, chat_id, chartdata, cache_store=False
):
    """
    More structured approach with better prompt engineering
    """
    # Build conversation string from history
    if history:
        conversation_string = "conversation:\n"
        for chat in history:
            conversation_string += f"Question: {chat[0]}\nAnswer: {chat[1]}\n\n"
    else:
        conversation_string = ""

    if cache_store:
        updated_chart_data = convert_chartdata_to_dict_v1(chartdata)
    else:
        updated_chart_data = chartdata
    logger.info("Processing with structured prompt ===================>")

    STRUCTURED_PROMPT = """
            You are and financial ai agent, provide answer for the user question based on the chartdata analysis provided.
            
            Context:
            - Previous conversation: {conversation_string}
            - User's current question: {question}
            - Available chart data: {chartdata}
            
            response: <answer>

            **important note**
            follow this response if below any points satisfied response: <p>intent_fallback_intent</p>
            1. if answer is not present and not relevant to given chartdata
            2. user question relevant to market and event data
            3. if any time based information like month, today, tomorrow, yesterday, event specific mentioned into user question
            4. if question was belongs to common intents like below list:
            common_intents = [
                # General
                "greet",
                "goodbye",
                "thank_you",
                "help",
                "affirm",
                "deny",
                "restart",
                "bot_identity",
            
                # Customer Support
                "report_issue",
                "check_status",
                "cancel_request",
                "escalate_issue",
            ]

            **note**:
            - Always respond with pure html format using html tags like <p>, <ul>, <li>. response should be directly rendered in UI with innerHTML.
            """

    # STRUCTURED_PROMPT = """
    #         Role: You are a financial AI agent specialized in chart data analysis.
    #
    #         Context:
    #         - Previous conversation: {conversation_string}
    #         - User's current question: {question}
    #         - Available chart data: {chartdata}
    #
    #         Task: Answer the user's question using the chart data provided.
    #
    #         Guidelines:
    #         1. Focus specifically on answering the user's question
    #         2. Extract relevant information from the chart data
    #         3. Maintain conversation continuity by referencing previous context when relevant
    #         4. Provide clear, data-driven insights
    #         5. Format response in clean HTML using <p>, <ul>, <li>, <strong> tags
    #
    #         Fallback conditions (respond with <p>intent_fallback_intent</p> if):
    #         - Chart data doesn't contain information relevant to the question
    #         - Question asks for real-time data not present in the chart
    #         - Question involves specific dates/times not covered in the data
    #         - Question is about market events not reflected in the chart data
    #
    #         Response:
    #         """

    prompt_template = PromptTemplate(
        input_variables=["question", "conversation_string", "chartdata"],
        template=STRUCTURED_PROMPT,
    )

    llm_chain = LLMChain(llm=get_openai_model(), prompt=prompt_template)

    try:
        result = llm_chain.run(
            question=question,
            conversation_string=conversation_string,
            chartdata=updated_chart_data,
        )

        if "intent_fallback_intent" in result:
            return ""

        if cache_store and chat_id:
            cache.set(f"cache_chart_data_{chat_id}", {"chartdata": updated_chart_data})

        return result.replace("```html", "").replace("```", "").strip()

    except Exception as e:
        logger.error(f"Error in structured processing: {e}")
        return ""


async def process_streaming_response(response_generator):
    has_final = False
    async for chunk in response_generator.body_iterator:
        # Decode chunk
        if isinstance(chunk, bytes):
            chunk = chunk.decode("utf-8")

        # Remove prefix "data: " and extra newlines
        chunk = chunk.replace("data: ", "").strip()
        try:
            payload = json.loads(chunk)
            if payload.get("type") == "final":
                has_final = True
                break
        except json.JSONDecodeError:
            continue

    return has_final


async def validating_question_layer(question, history, type):
    """
    More structured approach with better prompt engineering
    """

    # Build conversation string from history
    if history:
        conversation_string = "conversation:\n"
        for chat in history:
            conversation_string += f"Question: {chat[0]}\nAnswer: {chat[1]}\n\n"
    else:
        conversation_string = ""

    # STRUCTURED_PROMPT = """
    # You are a helpful AI assistant for a trading and analytics platform. Follow these rules strictly:
    #
    # Context:
    #     - Previous conversation: {conversation_string}
    #     - User's current question: {question}
    #
    # 1. If the user’s question is vague, ambiguous, or unclear:
    #    - Respond with a helpful suggestion.
    #    - Present **exactly 3 specific follow-up questions** for clarification.
    #    - Ask the user to choose one of them.
    #    - Response format should be:
    #
    # <suggestion>Your question is unclear. Please clarify what you mean.</suggestion>
    # <options>
    #   <option>Option 1 question</option>
    #   <option>Option 2 question</option>
    #   <option>Option 3 question</option>
    # </options>
    #
    # 2. If the user's question is clear, valid, and understandable (e.g., technical, trading-related, or platform-usage-related), respond with:
    #
    # <p>realtime</p>
    #
    # Examples:
    # Input: "how it works"
    # → Output:
    # <suggestion>Your question is unclear. Please clarify what you mean.</suggestion>
    # <options>
    #   <option>Do you want to know how trading strategies work?</option>
    #   <option>Do you want to know how the AI model analyzes charts?</option>
    #   <option>Are you asking about the working of the platform backend?</option>
    # </options>
    #
    # Input: "Explain RSI strategy for crypto"
    # → Output:
    # <p>realtime</p>
    # """

    if type == "deepsearch":
        STRUCTURED_PROMPT = """You are a financial assistant that analyzes user questions and conversation history to provide appropriate responses. Follow these exact rules:

    ## Response Rules

    ### Rule 1: Common Intents (Greeting, Thanks, Bye, Affirm)
    If user question relates to greetings (hello, hi, hii, how are you, good morning), thanks (thank you, thanks), goodbye (bye, goodbye, see you), or affirmation (yes, okay, welcome, sure):
    Response: <p>Could you please clarify what you’re asking about? Provide some more context.</p>

    ### Rule 2: Financial/Market Data Questions
    If user question relates to market data, stock data, future and options, forex, cryptocurrency, news, events, economic indicators, charts, technical analysis, or document-related financial questions:
    1. Analyze the question and conversation history
    2. If question is related to conversation history then return <p>realtime</p>
    3. Check if critical information is missing (specific stock name, timeframe, etc.)
    4. If information is missing and not related to conversation history then provide suggestions in this exact format:
    <suggestion>Please clarify what you need.</suggestion>
    <options>
      <option>Relevant question 1</option>
      <option>Relevant question 2</option>
      <option>Relevant question 3</option>
    </options>
    5. If information is complete, return <p>realtime</p>

    ### Rule 3: Context-Dependent Questions
    1. Check conversation history for relevant context
    2. If question depends or related on previous conversation context, return <p>realtime</p>

    ### Rule 4: Fallback Intent
    If question cannot be categorized or understood and not related to the conversation history
    Return: <p>Could you please clarify what you’re asking about? Provide some more context.</p>

    User Question: {user_question}
    Conversation History: {conversation_history}

    Response:"""
    else:
        STRUCTURED_PROMPT = """You are a financial assistant that analyzes user questions and conversation history to provide appropriate responses. Follow these exact rules:

            ## Response Rules

            ### Rule 1: Common Intents (Greeting, Thanks, Bye, Affirm)
            If user question relates to greetings (hello, hi, hii, how are you, good morning), thanks (thank you, thanks), goodbye (bye, goodbye, see you), or affirmation (yes, okay, welcome, sure):
            Response: <p>realtime</p>

            ### Rule 2: Financial/Market Data Questions
            If user question relates to market data, stock data, future and options, forex, cryptocurrency, news, events, economic indicators, charts, technical analysis, or document-related financial questions:
            1. Analyze the question and conversation history
            2. If question is related to conversation history then return <p>realtime</p>
            3. Check if critical information is missing (specific stock name, timeframe, etc.)
            4. If information is missing and not related to conversation history then provide suggestions in this exact format:
            <suggestion>Please clarify what you need.</suggestion>
            <options>
              <option>Relevant question 1</option>
              <option>Relevant question 2</option>
              <option>Relevant question 3</option>
            </options>
            5. If information is complete, return <p>realtime</p>

            ### Rule 3: Context-Dependent Questions
            1. Check conversation history for relevant context
            2. If question depends or related on previous conversation context, return <p>realtime</p>

            ### Rule 4: Fallback Intent
            If question cannot be categorized or understood and not related to the conversation history
            Return: <p>Could you please clarify what you’re asking about? Provide some more context.</p>

            User Question: {user_question}
            Conversation History: {conversation_history}

            Response:"""

    prompt_template = PromptTemplate(
        input_variables=["question", "conversation_string"], template=STRUCTURED_PROMPT
    )

    llm_chain = LLMChain(llm=get_openai_model(), prompt=prompt_template)

    try:
        response = llm_chain.run(
            user_question=question,
            conversation_history=conversation_string,
        )
        response = (
            response.replace("```html", "")
            .replace("```", "")
            .replace("suggestion", "p")
            .replace("options", "ul")
            .replace("option", "li")
            .strip()
        )
        return response

    except Exception as e:
        logger.error(f"Error in validating user's question: {e}")
        return "realtime"


async def handle_image_chat_stream(
    data: GenericChatSchema,
    params,
    user_question,
    track,
    history,
    chat_id,
    db: AsyncSession,
):
    try:
        logger.info(f"Running handle image chat for chat_id: {chat_id}")
        images = data.hidden_images or data.images or data.chat_images or []
        lens_id = data.lens_id or ""
        final_response = ""
        resp = json.dumps(
            {"loading_message": "We are analyzing chart data", "type": "loading"}
        )
        yield f"data: {resp}\n\n"
        chart_context = cache.get(f"cache_chart_data_{chat_id}") or {}
        if lens_id:
            chart_context = await get_chart_data_from_lens_id(
                lens_id, track, db, params, chart_context, chat_id
            )

        if chart_context:
            chartdata = chart_context.get("chartdata", [])

            resp = handle_chartdata_context_langchain(
                user_question, history, chat_id, chartdata, cache_store=False
            )
            if resp:
                final_response = resp

        if final_response:
            logger.info("final response already getting from chartsdata")

        else:
            response = chat_with_image_generic(
                images, user_question, track, history=history
            )
            # answer_text = re.sub(r"<.*?>", "", response).strip()
            response = response.replace("\n", "").replace("\r", "").strip("*")
            if response.lower() == "realtime":
                resp = json.dumps(
                    {
                        "loading_message": "We are searching on the internet",
                        "type": "loading",
                    }
                )
                yield f"data: {resp}\n\n"
                await asyncio.sleep(0.3)
                # Fetch real-time data
                # realtime_data = await web_scrape_search(user_question or "", flag=True)
                realtime_data = await web_scrape_search(user_question or "")

                # Format the response with clickable links
                response_text = realtime_data.get(
                    "answer", "Real-time data unavailable."
                )
                sources = realtime_data.get("sources", [])

                # Add formatted source links at the end of the response
                if sources:
                    response_text += "\n\nSources:\n"
                    for i, source in enumerate(sources, 1):
                        if isinstance(source, str) and source.startswith(
                            ("http://", "https://")
                        ):
                            response_text += f"[Read more]({source})"
                            if i < len(sources):
                                response_text += " | "

                final_response = response_text
            else:
                final_response = response

        result = {
            "answer": final_response,
        }
        resp = json.dumps(
            {
                "detail": result,
                "type": "data",
            }
        )
        # yield f"data: {resp}\n\n"
        # Store response in history and cache
        history.append([user_question, final_response])
        cache.set(f"chat_history_{chat_id}", history)
        chat_data = await save_chat(
            chat_id,
            data.parent,
            question=data.question,
            answer=final_response,
            edited=data.edited_question,
            reply=data.reply,
            images=data.images,
            chat_images=data.chat_images,
            chat_full_images=data.hidden_images,
            logo=data.logo,
            symbol=data.symbol,
            timeframe=data.timeframe,
        )
        logger.info(f"Returning response: {chat_id}")
        result = {
            "answer": final_response,
            "images": data.images,
            "id": chat_data.get("id"),
            "chat_id": chat_data.get("chat_id"),
        }
        resp = json.dumps(
            {
                "detail": result,
                "type": "final",
            }
        )
        yield f"data: {resp}\n\n"
        return

    except Exception as e:
        logger.error(f"Error in handle_image_chat_stream: {str(e)}", exc_info=True)
        resp = json.dumps(
            {
                "detail": "Internal Server Error",
                "type": "error",
            }
        )
        yield f"data: {resp}\n\n"
        send_alert_mail(traceback.format_exc())
        return


def calculate_strength_from_chart_data(chart_data: list[ChartDataItem]) -> dict:
    """Calculate bullish/bearish strength from chart data."""
    if not chart_data:
        return {"bullish_strength": 0, "bearish_strength": 0}

    bullish_count = 0
    bearish_count = 0

    for candle in chart_data:
        if candle.close > candle.open:
            bullish_count += 1
        elif candle.close < candle.open:
            bearish_count += 1

    total = bullish_count + bearish_count
    if total == 0:
        return {"bullish_strength": 0, "bearish_strength": 0}

    bullish_strength = round((bullish_count / total) * 100)
    bearish_strength = round((bearish_count / total) * 100)

    return {"bullish_strength": bullish_strength, "bearish_strength": bearish_strength}


class ChartDataEncoder(json.JSONEncoder):
    """Custom JSON encoder to handle special data types"""

    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        elif isinstance(obj, Decimal):
            return float(obj)
        elif hasattr(obj, '__dict__'):
            return obj.__dict__
        return super().default(obj)

async def analyze_chart_handler_web(
    request: Request,
    data: SimplifiedChartRequest_webV2,
    chart: ChartData,
    vuelensid: str,
    db: AsyncSession,
):
    try:
        chart_data = chart.chartData
        logger.info(f"Web-Chart analysis request: {data}")
        logger.info(f"Processing additional chart data: {len(chart_data)} items")

        if data.account_id:
            account_id = f"{request.state.customer_id}_{data.account_id}"
        else:
            account_id = f"{request.state.customer_id}_0"

        data.account_id = account_id

        # try:
        #     image_path, extension = download_image(data.images[0], data.account_id)
        #     encoded_image = encode_image(str(image_path))
        #     images_data.append(
        #         {
        #             "image_url": f"data:image/{extension};base64,{encoded_image}",
        #             "image_path": data.images[0],
        #         }
        #     )
        # except Exception as e:
        #     logger.error(f"Image processing failed: {str(e)}", exc_info=True)
        #     yield f'data: {json.dumps({"detail": f"Failed to process image: {str(e)}", "type": "error"})}\n\n'
        #     return

        async def call_llm(chart_data: List[BaseModel], system_msg: str, response_model: Optional[Any] = None):
            try:
                # Convert BaseModel items to dicts
                serialized_data = [item.model_dump() if isinstance(item, BaseModel) else item for item in chart_data]
                messages = [
                    {"role": "system", "content": system_msg},
                    {
                        "role": "user",
                        "content": json.dumps({"chart_data": serialized_data})
                    },
                ]
                if response_model:
                    response = client.beta.chat.completions.parse(
                        model=settings.GPT_4O_MINI_MODEL_NAME,
                        messages=messages,
                        response_format=response_model,
                    )
                    return response.choices[0].message.parsed.model_dump()
                else:
                    response = client.chat.completions.create(
                        model=settings.GPT_4O_MINI_MODEL_NAME,
                        messages=messages,
                    )
                    return response.choices[0].message.content
            except Exception as e:
                logger.error(f"LLM call failed: {str(e)}", exc_info=True)
                raise

        def safe_convert_int(value, default=0):
            try:
                return int(float(value))
            except (ValueError, TypeError):
                return default

        def format_date_range(start_time: datetime, end_time: datetime) -> str:
            return f"{start_time.strftime('%Y-%m-%d %H:%M')} - {end_time.strftime('%Y-%m-%d %H:%M')}"

        def convert_response(
            response_data: dict, start_time: datetime, end_time: datetime
        ) -> dict:
            bullish = safe_convert_int(response_data.get("bullish_strength", 0))
            bearish = safe_convert_int(response_data.get("bearish_strength", 0))
            return {
                "bullish_strength": bullish,
                "bearish_strength": bearish,
                "stock_price": data.current_price
                if data.current_price is not None
                else 0,
                "currency": (data.currency or "")[:3].upper(),
                "date_range": format_date_range(start_time, end_time),
                "timeframe": data.timeframe or "",
                "symbol": data.symbol or "",
                "title": f"{data.symbol} analysis" if data.symbol else "Chart analysis",
            }

        try:
            if chart_data:
                response_data = calculate_strength_from_chart_data(chart_data)
            else:
                system_message = """Analyze this financial chart data and extract ONLY the bullish and bearish strength percentages. Return as JSON with keys 'bullish_strength' and 'bearish_strength'."""
                response_data = await call_llm(
                    chart_data,
                    system_message,
                    WebChartAnalysisResult,
                )

            start_time = (
                datetime.strptime(chart_data[0].datetime, "%Y-%m-%dT%H:%M:%S.%fZ")
                if chart_data
                else datetime.now()
            )
            end_time = (
                datetime.strptime(chart_data[-1].datetime, "%Y-%m-%dT%H:%M:%S.%fZ")
                if chart_data
                else datetime.now()
            )

            merged_data = convert_response(response_data, start_time, end_time)
            source_info = (
                "based on structured chart data"
                if chart_data
                else "from the chart image"
            )

            # First yield - return basic chart data immediately
            yield f'data: {json.dumps({"detail": merged_data, "type": "final"})}\n\n'

            analysis_prompts = [
                {
                    "title": "Candlestick Overview",
                    "prompt": f"Analyze the candlestick patterns for {merged_data.get('symbol', 'chart')} from {merged_data['date_range']}. "
                    "Identify and explain significant candlestick formations (like Doji, Hammer, Engulfing patterns, etc.) "
                    "and their implications for market sentiment. Focus on patterns that appear at key price levels. "
                    "Highlight any multi-candle patterns if present.",
                },
                {
                    "title": "Technical Analysis",
                    "prompt": f"Analyze the technicals of {merged_data.get('symbol', 'this asset')} with bullish strength at {merged_data['bullish_strength']}% "
                    f"and bearish strength at {merged_data['bearish_strength']}% using {source_info}. "
                    "Include detailed analysis of support and resistance levels also mention price values at those levels "
                    "Identify support and resistance levels with reasoning and also tell the price where it exists"
                    "Analyze volume patterns if visible.",
                },
                {
                    "title": "Market Overview",
                    "prompt": f"Provide a comprehensive analysis of {merged_data.get('symbol', 'chart')} on {merged_data.get('timeframe', '')} timeframe from {merged_data['date_range']} {source_info}. "
                    "Focus on market structure, key price levels, and trends. Identify any visible chart patterns.",
                },
                {
                    "title": "Event Activity",
                    "prompt": f"Highlight significant price events for {merged_data.get('symbol', '')} during {merged_data['date_range']} {source_info}. "
                    "Correlate these events with any visible price reactions, volume spikes, or pattern formations in the chart.",
                },
            ]

            async def get_analysis(prompt):
                try:
                    system_msg = f"""You are a professional financial analyst. Focus on {prompt['title']}.

                    IMPORTANT: Format your response in HTML that can be directly rendered in a UI.
                    - Respond only in HTML rich text with tags like <strong>, <p>, <ul>, <li>, <h3>, <h4> etc.
                    - This should be directly rendered in the UI with innerHTML.
                    - Use appropriate HTML tags like:
                      * <h3> or <h4> for section headings
                      * <p> for paragraphs
                      * <ul> and <li> for lists
                      * <strong> for emphasis
                      * <em> for italics
                    - Do not use markdown formatting (no **, *, #, etc.)
                    - Ensure the HTML is well-formed and syntactically correct."""

                    analysis_text = await call_llm(
                        chart_data,
                        system_msg + "\n\n" + prompt["prompt"],
                    )
                    analysis_text = analysis_text.replace("```html", "").replace(
                        "```", ""
                    ).replace("\n", "").strip()
                    return {"title": prompt["title"], "analysis": analysis_text}
                except Exception as e:
                    logger.error(f"Failed analysis for {prompt['title']}: {str(e)}")
                    return {
                        "title": prompt["title"],
                        "analysis": f"<p>Analysis failed: {str(e)}</p>",
                    }

            analyses = await asyncio.gather(
                *[get_analysis(p) for p in analysis_prompts]
            )

        except Exception as e:
            logger.error(f"Chart analysis failed: {str(e)}", exc_info=True)
            yield f'data: {json.dumps({"detail": f"Chart analysis failed: {str(e)}", "type": "error"})}\n\n'
            return

        final_response = {**merged_data, "analyses": analyses}

        try:
            # json_data = []
            #
            # for item in chart_data:
            #     json_item = {
            #         "datetime": item.datetime if item.datetime else None,
            #         "open": float(item.open) if item.open is not None else None,
            #         "close": float(item.close) if item.close is not None else None,
            #         "high": float(item.high) if item.high is not None else None,
            #         "low": float(item.low) if item.low is not None else None,
            #         "indicators": item.indicators
            #         if hasattr(item, "indicators")
            #         else {},
            #         "drawings": item.drawings if hasattr(item, "drawings") else {},
            #         "idx": {
            #             "index": item.idx.index if hasattr(item.idx, "index") else None,
            #             "level": item.idx.level if hasattr(item.idx, "level") else None,
            #             "date": item.idx.date if hasattr(item.idx, "date") else None,
            #         }
            #         if hasattr(item, "idx") and item.idx
            #         else None,
            #     }
            #     json_data.append(json_item)
            lens_id = str(uuid.uuid4())
            stmt = insert(ScanImage).values(
                account=data.account_id,
                file_path="",
                hidden_file_path="",  # Empty since we don't have second image anymore
                bearish_strength=merged_data["bearish_strength"],
                bullish_strength=merged_data["bullish_strength"],
                price=merged_data["stock_price"],
                date_range=merged_data["date_range"],
                time_frame=merged_data["timeframe"],
                currency=merged_data["currency"],
                title=merged_data["title"],
                market_overview=analyses[1]["analysis"],
                technical_analysis=analyses[2]["analysis"],
                candlestick_overview=analyses[0]["analysis"],
                event_activity=analyses[3]["analysis"],
                symbol=merged_data["symbol"],
                symbol_logo=data.symbol,
                type="web",
                chartdata=json.dumps(chart, cls=ChartDataEncoder),
                lens_id=lens_id,
                vuelensid=vuelensid,  # Store the vuelensid for unified system
            )
            result = await db.execute(stmt)
            final_response["id"] = result.lastrowid
            final_response["lens_id"] = lens_id
            await db.commit()
        except Exception as e:
            logger.error(f"Database save failed: {str(e)}", exc_info=True)
            final_response["db_save_error"] = str(e)

        yield f'data: {json.dumps({"detail": final_response, "type": "combined_final"})}\n\n'
        logger.info(
            f"account id: {data.account_id} == Final response: {final_response}"
        )
        return

    except Exception as e:
        logger.error(f"Chart analysis failed: {str(e)}", exc_info=True)
        yield f'data: {json.dumps({"detail": "Internal Server Error", "type": "error"})}\n\n'
        send_alert_mail(traceback.format_exc())
        return


async def analyze_chart_handler(
    request: Request, data: SimplifiedChartRequest_chrome, db: AsyncSession
):
    logger.info(f"Web-Chart analysis request: {data}")
    images_data = []

    if data.account_id:
        account_id = f"{request.state.customer_id}_{data.account_id}"
    else:
        account_id = f"{request.state.customer_id}_0"

    data.account_id = account_id

    # Ensure we have exactly two images
    if len(data.images) < 2:
        raise HTTPException(status_code=400, detail="Exactly 2 images are required.")

    # Download and encode both images
    try:
        for image in data.images[:2]:  # Only process the first 2 images
            image_path, extension = download_image(image, data.account_id)
            encoded_image = encode_image(str(image_path))
            images_data.append(
                {
                    "image_url": f"data:image/{extension};base64,{encoded_image}",
                    "image_path": image,
                }
            )
    except Exception as e:
        logger.error(f"Image processing failed: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=400, detail=f"Failed to process image: {str(e)}"
        )

    # System message for initial chart analysis
    system_message = """
    Analyze the given financial chart image and return JSON with:
    - symbol (if detectable on top left of the image)
    - timeframe (if visible on the right of the symbol in image)
    - date_range (On the below of the image - must be in "from - to" format, e.g. "23 Dec - 26 Dec") and if 1st image is blank then let it be blank
    - bullish_strength and bearish_strength (MUST be integers between 0-100)
    
    IMPORTANT FOR BULLISH/BEARISH STRENGTH:
    - Carefully count the number of bullish vs bearishcandles
    - Calculate percentage: bullish_strength = (bullish_candles/total_candles)*100
    - Calculate percentage: bearish_strength = (bearish_candles/total_candles)*100
    - Ensure these values add up to 100%
    - If image is unclear or has no candles, use 50 for both values
    - NEVER return 0 for both values unless the image is completely blank
    
    - stock_price (current price from the second image, numeric value only)
    - currency (3-letter code like if pairs(EURUSD, SOLUSD) then last 3 letters if available, otherwise leave empty)
    - title for the analysis

    For stock_price:
    - Extract the exact numeric value shown as current price
    - Ignore any currency symbols or text
    - Format as number with decimal point if needed (e.g. 187.83)
    - If unavailable, use 0

    Example Response:
    {
        "symbol": "AAPL",
        "timeframe": "1d",
        "date_range": "23 Dec - 26 Dec",
        "bullish_strength": 65,
        "bearish_strength": 35,
        "stock_price": 187.83,
        "currency": "USD",
        "title": "Apple Inc. Daily Chart Analysis"
    }
    """

    async def call_llm(image_url: str, system_msg: str, response_model=None):
        """Helper function to send a request to the LLM model."""
        import time
        start_time = time.time()

        try:
            messages = [
                {"role": "system", "content": system_msg},
                {
                    "role": "user",
                    "content": [{"type": "image_url", "image_url": {"url": image_url}}],
                },
            ]

            if response_model:
                response = client.beta.chat.completions.parse(
                    model=settings.MODEL_NAME,
                    messages=messages,
                    response_format=response_model,
                )
                result = response.choices[0].message.parsed.model_dump()
            else:
                response = client.chat.completions.create(
                    model=settings.MODEL_NAME,
                    messages=messages,
                )
                result = response.choices[0].message.content

            # Track cost
            duration_ms = (time.time() - start_time) * 1000
            from app.utils.comprehensive_cost_tracker import track_openai_response
            track_openai_response(
                model_name=settings.MODEL_NAME,
                function_name="call_llm_image_analysis",
                messages=messages,
                response=response,
                duration_ms=duration_ms
            )

            return result
        except Exception as e:
            logger.error(f"LLM call failed: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=500, detail=f"LLM processing failed: {str(e)}"
            )

    def safe_convert_int(value, default=0):
        try:
            return int(float(value))
        except (ValueError, TypeError):
            return default

    def format_date_range(date_range: str) -> str:
        """Format date range to ensure consistent 'from - to' format"""
        if not date_range:
            return ""

        # Remove any extra spaces around the dash
        date_range = date_range.strip()
        date_range = re.sub(r"\s*-\s*", " - ", date_range)

        # Ensure proper spacing and formatting
        parts = date_range.split(" - ")
        if len(parts) == 2:
            return f"{parts[0].strip()} - {parts[1].strip()}"
        return date_range

    def convert_response(response_data: dict, first_image_data: dict) -> dict:
        """Merge data, giving priority to first image for strength values and trend."""
        bullish = safe_convert_int(first_image_data.get("bullish_strength", 0))
        bearish = safe_convert_int(first_image_data.get("bearish_strength", 0))

        stock_price = response_data.get("stock_price", "0")
        currency = response_data.get("currency", "")

        # Clean and format currency (3-letter code or empty)
        currency = currency[:3].upper() if currency and len(currency) >= 3 else ""

        # Format date range
        date_range = format_date_range(response_data.get("date_range", ""))

        return {
            "bullish_strength": bullish,
            "bearish_strength": bearish,
            "stock_price": stock_price,
            "currency": currency,
            "date_range": date_range,
            "timeframe": response_data.get("timeframe", ""),
            "symbol": response_data.get("symbol", ""),
            "title": response_data.get("title", ""),
        }

    try:
        # Call LLM for both images in parallel
        # response_data_1, response_data_2 = await asyncio.gather(
        #     call_llm(images_data[0]["image_url"], system_message, WebChartAnalysisResult),
        #     call_llm(images_data[1]["image_url"], system_message, WebChartAnalysisResult)
        # )
        response_data_1 = await call_llm(
            images_data[0]["image_url"], system_message, WebChartAnalysisResult
        )
        response_data_2 = await call_llm(
            images_data[1]["image_url"], system_message, WebChartAnalysisResult
        )

        # Merge the results
        merged_data = convert_response(response_data_2, response_data_1)
        yield f'data: {json.dumps({"detail": merged_data, "type": "final"})}\n\n'

        # Define source info for analysis prompts
        source_info = "from the chart image"

        # Define optimized analysis prompts
        analysis_prompts = [
            {
                "title": "Candlestick Overview",
                "prompt": f"Analyze the candlestick patterns for {merged_data.get('symbol', 'chart')} from {merged_data['date_range']}. "
                "Focus ONLY on the FIRST image. "
                "Identify and explain significant candlestick formations (like Doji, Hammer, Engulfing patterns, etc.) "
                "and their implications for market sentiment. Focus on patterns that appear at key price levels. "
                "Highlight any multi-candle patterns if present. "
                "Be specific about the patterns you see and their exact locations on the chart.",
            },
            {
                "title": "Market Overview",
                "prompt": f"Provide a comprehensive analysis of the {merged_data.get('symbol', 'chart')} on {merged_data.get('timeframe', '')} timeframe from {merged_data.get('date_range', 'the shown period')}. "
                "Focus on market structure, key price levels, and overall price action. "
                "Identify support/resistance levels and market phases. "
                "If image is blank, indicate analysis not possible.",
            },
            {
                "title": "Technical Analysis",
                "prompt": f"Analyze the technicals of {merged_data.get('symbol', 'this asset')} with bullish strength at {merged_data['bullish_strength']}% "
                f"and bearish strength at {merged_data['bearish_strength']}% using {source_info}. "
                "Focus ONLY on the FIRST image. "
                "Include detailed analysis of support and resistance levels and mention price values at those levels. "
                "Identify specific support and resistance levels with reasoning and also tell the exact price where each exists. "
                "Analyze volume patterns if visible. "
                "Be specific about indicators, trend lines, and price action patterns visible in the chart.",
            },
            {
                "title": "Event Activity",
                "prompt": f"Analyze significant price movements and events for {merged_data.get('symbol', '')} during {merged_data.get('date_range', 'this period')}. "
                "Focus on volume spikes, price gaps, and sudden movements that might indicate market events or news impact. "
                "If image is blank, indicate analysis not possible.",
            },
        ]

        # Generate analyses for each prompt in parallel
        async def get_analysis(prompt):
            try:
                system_msg = f"""You are a professional financial analyst. Focus on {prompt['title']}.

                    IMPORTANT: Format your response in HTML that can be directly rendered in a UI.
                    - Respond only in HTML rich text with tags like <strong>, <p>, <ul>, <li>, <h3>, <h4> etc.
                    - This should be directly rendered in the UI with innerHTML.
                    - Use appropriate HTML tags like:
                      * <h3> or <h4> for section headings
                      * <p> for paragraphs
                      * <ul> and <li> for lists
                      * <strong> for emphasis
                      * <em> for italics
                    - Do not use markdown formatting (no **, *, #, etc.)
                    - Ensure the HTML is well-formed and syntactically correct."""

                analysis_text = await call_llm(
                    images_data[0]["image_url"], system_msg + "\n\n" + prompt["prompt"]
                )
                analysis_text = analysis_text.replace("```html", "").replace("```", "")

                return {"title": prompt["title"], "analysis": analysis_text}
            except Exception as e:
                logger.error(
                    f"Failed to generate analysis for {prompt['title']}: {str(e)}"
                )
                return {
                    "title": prompt["title"],
                    "analysis": f"Analysis generation failed for this section. Error: {str(e)}",
                }

        # Run all analyses in parallel
        analyses = await asyncio.gather(
            *[get_analysis(prompt) for prompt in analysis_prompts]
        )
        # yield f'data: {json.dumps({"detail": analyses, "type": "analyses_final"})}\n\n'

    except Exception as e:
        logger.error(f"Chart analysis failed: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Chart analysis failed: {str(e)}")
    finally:
        # Clean up images
        for image in images_data:
            remove_image(image["image_path"])

    # Prepare final response
    final_response = {**merged_data, "analyses": analyses}

    try:
        logger.info("Preparing to save scan result to DB.")

        stmt = insert(ScanImage).values(
            account=data.account_id,
            file_path=data.images[0],
            hidden_file_path=data.images[1],
            bearish_strength=merged_data["bearish_strength"],
            bullish_strength=merged_data["bullish_strength"],
            price=merged_data["stock_price"],
            date_range=merged_data["date_range"],
            time_frame=merged_data["timeframe"],
            currency=merged_data["currency"],
            title=merged_data["title"],
            market_overview=analyses[1]["analysis"],
            technical_analysis=analyses[2]["analysis"],
            candlestick_overview=analyses[0]["analysis"],
            event_activity=analyses[3]["analysis"],
            symbol=merged_data["symbol"],
            symbol_logo=data.symbol,
            type="chrome",
        )

        logger.debug(f"Inserting into ScanImage: {stmt}")
        result = await db.execute(stmt)

        logger.info("Insert executed. Committing to DB...")
        final_response["id"] = result.lastrowid

        await db.commit()
        logger.info(
            f"Chart analysis saved successfully with ID: {final_response['id']}"
        )

    except Exception as e:
        logger.error(f"Database save failed: {str(e)}", exc_info=True)
        final_response["db_save_error"] = str(e)

    # Final yield with ID if save was successful
    if "id" in final_response:
        yield f'data: {json.dumps({"detail": final_response, "type": "final"})}\n\n'


async def handle_image_chat_stream_v3(
    data: GenericChatSchema,
    params,
    user_question,
    track,
    history,
    chat_id,
    db: AsyncSession,
):
    try:
        logger.info(f"Running handle image chat for chat_id: {chat_id}")
        images = (
            data.hidden_images
            or data.images
            or data.chat_images
            or cache.get(f"cache_images_in_chat_{chat_id}")
            or []
        )
        lens_id = data.lens_id or ""
        final_response = ""
        cache.set(f"cache_images_in_chat_{chat_id}", images)
        resp = json.dumps(
            {"loading_message": "We are analyzing chart data", "type": "loading"}
        )
        yield f"data: {resp}\n\n"
        chart_context = cache.get(f"cache_chart_data_{chat_id}") or {}
        if lens_id:
            chart_context = await get_chart_data_from_lens_id(
                lens_id, track, db, params, chart_context, chat_id
            )

        if chart_context:
            chartdata = chart_context.get("chartdata", [])
            resp = handle_chartdata_context_langchain(
                user_question, history, chat_id, chartdata, cache_store=False
            )
            if resp:
                final_response = resp

        if final_response:
            logger.info("final response already getting from chartsdata")
        else:
            response = chat_with_image_generic_v3(
                images, user_question, track, history=history
            )
            if "intent_fallback" not in response:
                # answer_text = re.sub(r"<.*?>", "", response).strip()
                response = response.replace("\n", "").replace("\r", "").strip("*")
                if response.lower() == "realtime":
                    resp = json.dumps(
                        {
                            "loading_message": "We are searching on the internet",
                            "type": "loading",
                        }
                    )
                    yield f"data: {resp}\n\n"
                    await asyncio.sleep(0.3)
                    # Fetch real-time data
                    # realtime_data = await web_scrape_search(user_question or "", flag=True)
                    realtime_data = await web_scrape_search_v3(user_question or "")
                    # Format the response with clickable links
                    response_text = realtime_data.get(
                        "answer", "Real-time data unavailable."
                    )
                    sources = realtime_data.get("sources", [])
                    # Add formatted source links at the end of the response
                    if sources:
                        response_text += "\n\nSources:\n"
                        for i, source in enumerate(sources, 1):
                            if isinstance(source, str) and source.startswith(
                                ("http://", "https://")
                            ):
                                response_text += f"[Read more]({source})"
                                if i < len(sources):
                                    response_text += " | "
                    final_response = response_text
                else:
                    final_response = response

        if final_response:
            result = {
                "answer": final_response,
            }
            resp = json.dumps(
                {
                    "detail": result,
                    "type": "data",
                }
            )
            # yield f"data: {resp}\n\n"
            # Store response in history and cache
            history.append([user_question, final_response])
            cache.set(f"chat_history_{chat_id}", history)
            chat_data = await save_chat(
                chat_id,
                data.parent,
                question=data.question,
                answer=final_response,
                edited=data.edited_question,
                reply=data.reply,
                images=data.images,
                chat_images=data.chat_images,
                chat_full_images=data.hidden_images,
                logo=data.logo,
                symbol=data.symbol,
                timeframe=data.timeframe,
            )
            logger.info(f"Returning response: {chat_id}")
            result = {
                "answer": final_response,
                "images": data.images,
                "id": chat_data.get("id"),
                "chat_id": chat_data.get("chat_id"),
            }
            resp = json.dumps(
                {
                    "detail": result,
                    "type": "final",
                }
            )
            yield f"data: {resp}\n\n"
            return

    except Exception as e:
        logger.error(f"Error in handle_image_chat_stream: {str(e)}", exc_info=True)
        resp = json.dumps(
            {
                "detail": "Internal Server Error",
                "type": "error",
            }
        )
        yield f"data: {resp}\n\n"
        send_alert_mail(traceback.format_exc())
        return


async def streaming_generic_chat_service_v3(
    db: AsyncSession,
    chat_id: str,
    account_id: int,
    trading_token: str,
    params: Params,
    lens_id: str,
    data: GenericChatSchema,
    generic_chat_history: list = [],
    is_websearch_enabled: bool = False,
    question: Optional[str] = None,
    reply: Optional[str] = None,
    token: str = "",
    track: str = "",
    selected_category: Optional[str] = None,
    selected_option: Optional[str] = None,
    background_tasks: BackgroundTasks = None,
    parent: str = "",
    edited_question: bool = False,
    original_question="",
    selections=None,
    file_paths=[],
):
    try:
        history_key = f"chat_history_{chat_id}"
        chat_context_key = f"chat_context_{chat_id}"
        chat_context = cache.get(chat_context_key) or {}
        chart_context = cache.get(f"cache_chart_data_{chat_id}") or {}
        is_lens_id = False

        if lens_id:
            is_lens_id = True
            chart_context = await get_chart_data_from_lens_id(
                lens_id, track, db, params, chart_context, chat_id
            )

        if chart_context:
            logger.info("coming into chart data functionality....")
            chartdata = chart_context.get("chartdata", [])
            resp = handle_chartdata_context_langchain(
                question, generic_chat_history, chat_id, chartdata, cache_store=False
            )
            if resp:
                if is_lens_id:
                    save_chat_data = await save_chat(
                        chat_id,
                        parent,
                        question=original_question,
                        answer=resp,
                        edited=edited_question,
                        reply=reply,
                        lens_id=lens_id,
                        selections=selections,
                        file_paths=file_paths,
                    )
                else:
                    save_chat_data = await save_chat(
                        chat_id,
                        parent,
                        question=original_question,
                        answer=resp,
                        edited=edited_question,
                        reply=reply,
                        selections=selections,
                        file_paths=file_paths,
                    )
                update_chat_history(history_key, generic_chat_history, question, resp)
                result = {
                    "answer": resp,
                    "chat_id": save_chat_data.get("chat_id"),
                    "id": save_chat_data.get("id"),
                }
                resp = json.dumps(
                    {
                        "detail": result,
                        "type": "final",
                    }
                )
                yield f"data: {resp}\n\n"
                return

        # Handle image-based chat requests
        if not lens_id and (data.hidden_images or data.images or data.chat_images):
            images = data.hidden_images or data.images or data.chat_images or []
            resp = json.dumps(
                {"loading_message": "We are analyzing chart data", "type": "loading"}
            )
            yield f"data: {resp}\n\n"
            response = chat_with_image_generic_v3(
                images, question, track, history=generic_chat_history
            )
            response = response.replace("\n", "").replace("\r", "").strip("*")
            cache.set(f"cache_images_in_chat_{chat_id}", images)
            if response.lower() == "realtime":
                resp = json.dumps(
                    {
                        "loading_message": "We are searching on the internet",
                        "type": "loading",
                    }
                )
                yield f"data: {resp}\n\n"
                await asyncio.sleep(0.3)
                # Fetch real-time data
                # realtime_data = await web_scrape_search(user_question or "", flag=True)
                realtime_data = await web_scrape_search_v3(question or "")
                # Format the response with clickable links
                response_text = realtime_data.get(
                    "answer", "Real-time data unavailable."
                )
                sources = realtime_data.get("sources", [])
                # Add formatted source links at the end of the response
                if sources:
                    response_text += "\n\nSources:\n"
                    for i, source in enumerate(sources, 1):
                        if isinstance(source, str) and source.startswith(
                            ("http://", "https://")
                        ):
                            response_text += f"[Read more]({source})"
                            if i < len(sources):
                                response_text += " | "
                final_response = response_text
            else:
                final_response = response

            # Store response in history and cache
            chat_data = await save_chat(
                chat_id,
                data.parent,
                question=data.question,
                answer=final_response,
                edited=data.edited_question,
                reply=data.reply,
                images=data.images,
                chat_images=data.chat_images,
                chat_full_images=data.hidden_images,
                logo=data.logo,
                symbol=data.symbol,
                timeframe=data.timeframe,
            )
            update_chat_history(
                history_key, generic_chat_history, question, final_response
            )
            logger.info(f"Returning response: {chat_id}")
            result = {
                "answer": final_response,
                "images": data.images,
                "id": chat_data.get("id"),
                "chat_id": chat_data.get("chat_id"),
            }
            resp = json.dumps(
                {
                    "detail": result,
                    "type": "final",
                }
            )
            yield f"data: {resp}\n\n"
            return

        if chat_context:
            resp = handle_context_call_with_chain(
                track, question, generic_chat_history, chat_id
            )
            if resp:
                save_chat_data = await save_chat(
                    chat_id,
                    parent,
                    question=original_question,
                    answer=resp,
                    edited=edited_question,
                    reply=reply,
                    selections=selections,
                    file_paths=file_paths,
                )
                update_chat_history(history_key, generic_chat_history, question, resp)
                result = {
                    "answer": resp,
                    "chat_id": save_chat_data.get("chat_id"),
                    "id": save_chat_data.get("id"),
                }
                resp = json.dumps(
                    {
                        "detail": result,
                        "type": "final",
                    }
                )
                yield f"data: {resp}\n\n"
                return

        resp = await get_answer_based_on_faqs(question=question, type="simple")
        if resp:
            resp = resp[0]
            if (
                "<p>Could you please clarify what you’re asking about? Provide some more context.</p>"
                not in resp
            ):
                save_chat_data = await save_chat(
                    chat_id,
                    parent,
                    question=original_question,
                    answer=resp,
                    edited=edited_question,
                    reply=reply,
                    selections=selections,
                    file_paths=file_paths,
                )
                update_chat_history(history_key, generic_chat_history, question, resp)
                result = {
                    "answer": resp,
                    "chat_id": save_chat_data.get("chat_id"),
                    "id": save_chat_data.get("id"),
                }
                resp = json.dumps(
                    {
                        "detail": result,
                        "type": "final",
                    }
                )
                yield f"data: {resp}\n\n"
                return

        response = validate_user_question(question, chat_id, generic_chat_history)
        print("answer from validation: ", response)
        if response["is_realtime"]:
            question = response["new_question"]
        else:
            print("coming to here")
            save_chat_data = await save_chat(
                chat_id,
                parent,
                question=original_question,
                answer=response["answer"],
                edited=edited_question,
                reply=reply,
                selections=selections,
                file_paths=file_paths,
            )
            update_chat_history(
                history_key, generic_chat_history, original_question, response["answer"]
            )

            result = {
                "answer": response["answer"],
                "chat_id": save_chat_data.get("chat_id"),
                "id": save_chat_data.get("id"),
            }
            resp = json.dumps(
                {
                    "detail": result,
                    "type": "final",
                }
            )
            yield f"data: {resp}\n\n"
            return

        reusable_response = get_reusable_response(prompt=question)

        if reusable_response is not None:
            save_chat_data = await save_chat(
                chat_id,
                parent,
                question=original_question,
                answer=reusable_response,
                edited=edited_question,
                reply=reply,
                selections=selections,
                file_paths=file_paths,
            )
            update_chat_history(
                history_key, generic_chat_history, original_question, reusable_response
            )
            result = {
                "answer": reusable_response,
                "chat_id": save_chat_data.get("chat_id"),
                "id": save_chat_data.get("id"),
            }
            resp = json.dumps(
                {
                    "detail": result,
                    "type": "final",
                }
            )
            yield f"data: {resp}\n\n"
            return

        cached_answer = get_cached_qa(question)

        if cached_answer is not None:
            save_chat_data = await save_chat(
                chat_id,
                parent,
                question=original_question,
                answer=cached_answer,
                edited=edited_question,
                reply=reply,
                selections=selections,
                file_paths=file_paths,
            )
            update_chat_history(
                history_key, generic_chat_history, original_question, cached_answer
            )
            result = {
                "answer": cached_answer,
                "chat_id": save_chat_data.get("chat_id"),
                "id": save_chat_data.get("id"),
            }
            resp = json.dumps(
                {
                    "detail": result,
                    "type": "final",
                }
            )
            yield f"data: {resp}\n\n"
            return

        # resp = json.dumps({"loading_message": "Please wait...", "type": "loading"})
        # yield f"data: {resp}\n\n"
        user_question = (
            f"Reply to: {reply}\n\nQuestion: {question}" if reply else question
        )
        if is_websearch_enabled:
            user_question += " Please search the internet for the answer."
        logger.info(f"User question: {user_question}")
        previous_question_check = check_question_from_history_v3(
            user_question, generic_chat_history[-3:]
        )

        try:
            test_question = json.loads(previous_question_check)
        except Exception as e:
            test_question = {
                "response": "Null",
                "is_new_question": previous_question_check,
            }

        if test_question.get("response") != "Null":
            result = {"answer": test_question["response"], "chat_id": chat_id}
            # await save_chat_to_db(chat_id, question, reply, test_question["response"], response_id, chat_type="NORMAL", question_mode="generic")
            chat_data = await save_chat(
                chat_id,
                parent,
                question=original_question,
                answer=test_question["response"],
                edited=edited_question,
                reply=reply,
                selections=selections,
                file_paths=file_paths,
            )
            update_chat_history(
                history_key,
                generic_chat_history,
                original_question,
                test_question["response"],
            )
            result["id"] = chat_data.get("id")
            result["chat_id"] = chat_data.get("chat_id")
            resp = json.dumps(
                {
                    "detail": result,
                    "type": "final",
                }
            )
            yield f"data: {resp}\n\n"
            # cache_qa(question, test_question["response"])
            return

        # If is_new_question exists, use it as user_question
        if test_question.get("is_new_question") != "Null":
            user_question = test_question["is_new_question"]
        else:
            user_question = question

        # Fetch account details
        try:
            query = f"SELECT token, customer FROM {settings.DATABASE_5.split('/')[-1]}.accounts WHERE id = :account_id"
            params = {"account_id": account_id}

            result = build_metric_json_data(
                metric_execute_query(query, settings.DATABASE_5, params)
            )

            account_id = result[0]["token"]
            customer_id = str(result[0]["customer"])
        except Exception as e:
            logger.error(f"Error fetching account data: {e}")
            customer_id = account_id = ""

        decoded_customer_id = track.split("_")[0]
        logger.info(
            f"Customer ID ====>: {customer_id}, Decoded ====>: {decoded_customer_id}, Account ID ====>: {account_id}"
        )
        if selected_category:
            resp = json.dumps(
                {
                    "loading_message": f"Please wait, we're are analyzing data for {selected_category} in vuetra's database",
                    "type": "loading",
                }
            )
            yield f"data: {resp}\n\n"
            answer = ""
            try:
                async for chunk in websearch_stream_handler(user_question):
                    if chunk["type"] == "answer":
                        resp = json.dumps(
                            {"detail": {"answer": chunk["data"]}, "type": "data"}
                        )
                        yield f"data: {resp}\n\n"
                        answer += chunk["data"]
                    else:
                        resp = chunk.get("data", {})
                        break
            except StopIteration:
                pass
            update_chat_history(
                history_key, generic_chat_history, user_question, answer
            )

            result = {"answer": answer, "source": resp.get("sources")}

            chat_data = await save_chat(
                chat_id,
                parent,
                question=original_question,
                answer=answer,
                edited=edited_question,
                reply=reply,
                source=resp.get("sources"),
                selections=selections,
                file_paths=file_paths,
            )
            result["id"] = chat_data.get("id")
            result["chat_id"] = chat_data.get("chat_id")
            resp = json.dumps({"detail": result, "type": "final"})

            yield f"data: {resp}\n\n"
            cache_qa(question, answer, ex=60 * 60 * 2)
            return

        system_prompt = (
            f"- Note: Ensure using the user's ID in queries where possible. Unique ID: {account_id}"
            + first_prompt
        )

        logger.info("Calling classify_question function")
        if test_question.get("external_answer") != "Null":
            first_result = await classify_question_v3(user_question, system_prompt)
        else:
            first_result = await classify_question_v3(user_question, system_prompt)
        logger.info("classify_question function executed successfully")
        cleaned_result = (
            first_result.replace("`", "").replace("json", "").replace("\n", "")
        )
        json_content = extract_json_content(cleaned_result)
        logger.info(f"json_content: {json_content}")
        if background_tasks:
            background_tasks.add_task(prepare_dataset, user_question, json_content)
        logger.info(f"json_content: {json_content}")
        result = {}

        if json_content is None:
            update_chat_history(
                history_key, generic_chat_history, user_question, cleaned_result
            )
            result = {"answer": cleaned_result, "chat_id": chat_id}
            # await save_chat_to_db(chat_id, question,reply, result["answer"], response_id, chat_type="NORMAL", question_mode="generic")
            chat_data = await save_chat(
                chat_id,
                parent,
                question=original_question,
                answer=result["answer"],
                edited=edited_question,
                reply=reply,
                selections=selections,
                file_paths=file_paths,
            )
            result["id"] = chat_data.get("id")
            result["chat_id"] = chat_data.get("chat_id")
            resp = json.dumps(
                {
                    "detail": result,
                    "type": "final",
                }
            )
            yield f"data: {resp}\n\n"
            cache_qa(question, result["answer"], ex=60 * 60 * 24 * 30)
            return

        # Step 2: Handle specific JSON content cases
        if json_content and "answer" in json_content:
            update_chat_history(
                history_key, generic_chat_history, user_question, json_content["answer"]
            )
            result = {"answer": json_content["answer"], "chat_id": chat_id}
            # await save_chat_to_db(chat_id, question,reply, result["answer"], response_id, chat_type="NORMAL", question_mode="generic")
            chat_data = await save_chat(
                chat_id,
                parent,
                question=original_question,
                answer=result["answer"],
                edited=edited_question,
                reply=reply,
                selections=selections,
                file_paths=file_paths,
            )
            result["id"] = chat_data.get("id")
            result["chat_id"] = chat_data.get("chat_id")
            resp = json.dumps(
                {
                    "detail": result,
                    "type": "final",
                }
            )
            yield f"data: {resp}\n\n"
            cache_qa(question, result["answer"], ex=60 * 60 * 24 * 30)
            return
        if json_content:
            sql_query_db5 = json_content.get("sql_query_db5")
            if sql_query_db5 and sql_query_db5.lower() == "websocket":
                resp = json.dumps(
                    {"loading_message": "We're analyzing data", "type": "loading"}
                )
                yield f"data: {resp}\n\n"
                answer = execute(trading_token, user_question)
                update_chat_history(
                    history_key, generic_chat_history, user_question, answer
                )
                result = {"answer": answer, "chat_id": chat_id}
            sql_query_db1 = json_content.get("sql_query_db1")
            if sql_query_db1 and sql_query_db1.lower() == "trade":
                answer = await trade_question_v3(user_question, trade_prompt)
                cleaned_result = (
                    answer.replace("`", "").replace("json", "").replace("\n", "")
                )
                json_decoded_content = extract_json_content(cleaned_result)
                json_content["sql_query_db1"] = json_decoded_content.get(
                    "sql_query_db1"
                )
                json_content["chart_type"] = json_decoded_content.get("chart_type")
                if background_tasks:
                    background_tasks.add_task(
                        prepare_dataset, user_question, json_content
                    )
            sql_query_db8 = json_content.get("sql_query_db8")
            if sql_query_db8 and sql_query_db8.lower() == "fmpdata":
                resp = json.dumps(
                    {
                        "loading_message": "We're analysing financial data",
                        "type": "loading",
                    }
                )
                yield f"data: {resp}\n\n"
                answer = await fmp_execute(user_question, chat_id)
                print("answer from fmp_execute: ", answer)
                if answer["data"] and "Unable to generate" not in answer["summary"]:
                    update_chat_history(
                        history_key,
                        generic_chat_history,
                        user_question,
                        answer["summary"],
                    )
                    if "chart_data" in answer.keys():
                        result = {
                            "answer": answer["summary"],
                            "chat_id": chat_id,
                            "chart_data": answer["chart_data"],
                        }
                    else:
                        result = {"answer": answer["summary"], "chat_id": chat_id}
            sql_query_db9 = json_content.get("sql_query_db9")
            if sql_query_db9 and sql_query_db9.lower() == "websearch":
                reps = json.dumps(
                    {"loading_message": "Searching the internet", "type": "loading"}
                )
                yield f"data: {reps}\n\n"
                answer = await web_scrape_search(user_question)
                update_chat_history(
                    history_key, generic_chat_history, user_question, answer["answer"]
                )
                result = {
                    "answer": answer["answer"],
                    "chat_id": chat_id,
                    "source": answer["sources"],
                }

            sql_query_db10 = json_content.get("sql_query_db10")
            if sql_query_db10 and sql_query_db10.lower() == "economicindicatordata":
                answer = await rds_sql_agent(
                    user_question, "economicindicatordata", track
                )
                update_chat_history(
                    history_key, generic_chat_history, user_question, answer["output"]
                )
                result = {"answer": answer["output"], "chat_id": chat_id}

            if result:  # nned to update if result is null here
                answer = await check_response_relevance(
                    user_question, generic_chat_history, result["answer"]
                )
                if type(answer) == dict and "sources" in answer.keys():
                    result = {
                        "answer": answer["answer"],
                        "chat_id": chat_id,
                        "source": answer["sources"],
                    }
                else:
                    result["answer"] = result.get("answer")

                update_chat_history(
                    history_key, generic_chat_history, user_question, result["answer"]
                )
                # await save_chat_to_db(chat_id, question,reply, result["answer"], response_id, chat_type="NORMAL", question_mode="generic")
                chat_data = await save_chat(
                    chat_id,
                    parent,
                    question=original_question,
                    answer=result["answer"],
                    edited=edited_question,
                    reply=reply,
                    selections=selections,
                    file_paths=file_paths,
                )
                result["id"] = chat_data.get("id")
                result["chat_id"] = chat_data.get("chat_id")
                resp = json.dumps(
                    {
                        "detail": result,
                        "type": "final",
                    }
                )
                yield f"data: {resp}\n\n"
                cache_qa(question, result["answer"], ex=60 * 60 * 2)
                return
        # Step 3: Process database queries
        result_db, event_ids, news_ids, metric_json = [], {}, [], None
        chart_type = json_content.pop("chart_type", None)
        try:
            del json_content["sql_query_db10"]
            del json_content["sql_query_db9"]
            del json_content["sql_query_db8"]
        except:
            print("Error coming from removing checked database")
        if (
            json_content.get("sql_query_db1") != "null"
            and customer_id != decoded_customer_id
        ):
            logger.error("Unauthorized trade data access attempted")
            # await save_chat_to_db(chat_id, question,reply, "You don't have any trade data.", response_id, chat_type="NORMAL", question_mode="generic")
            chat_data = await save_chat(
                chat_id,
                parent,
                question=original_question,
                answer="You don't have any trade data.",
                edited=edited_question,
                reply=reply,
                selections=selections,
                file_paths=file_paths,
            )
            result = {"answer": "You don't have any trade data."}
            result["id"] = chat_data.get("id")
            result["chat_id"] = chat_data.get("chat_id")
            resp = json.dumps(
                {
                    "detail": result,
                    "type": "final",
                }
            )
            yield f"data: {resp}\n\n"
            cache_qa(question, "You don't have any trade data.")
            return
        loading_messages = {
            "sql_query_db1": "We are gathering your trade data.",
            "sql_query_db2": "We are gathering event data.",
            "sql_query_db3": "We are gathering news data.",
        }
        db_result = []
        for db_key, query in json_content.items():
            if query and query != "null":
                logger.info(f"Processing SQL query for {db_key}")

                # Send loading message if applicable
                if db_key in loading_messages:
                    resp = json.dumps(
                        {"loading_message": loading_messages[db_key], "type": "loading"}
                    )
                    yield f"data: {resp}\n\n"

                db_result = await process_sql_query(
                    db_key, query, account_id, DATABASE_SELECTOR
                )
                logger.info(f"DB RESULT: {db_result}")
                result_db.append(db_result)

                if db_key == "sql_query_db2":
                    event_ids = {
                        row.get("date", "").split("T")[0]: [
                            row.get("ID") for row in db_result
                        ]
                        for row in db_result
                    }
                elif db_key == "sql_query_db3":
                    news_ids = [row.get("news_id") for row in db_result]
                elif db_key in {"sql_query_db1", "sql_query_db7"}:
                    metric_json = db_result
        if db_result:
            # search flow start
            search_queries = {
                "sql_query_db2": event_ids,
                "sql_query_db3": news_ids,
                "sql_query_db4": db_result,
                "sql_query_db6": db_result,
                "sql_query_db7": db_result,
            }
        else:
            search_queries = {
                "sql_query_db2": event_ids,
                "sql_query_db3": news_ids,
            }

        for db_key, query in json_content.items():
            if query != "null" and not search_queries.get(
                db_key, True
            ):  # Check if query is valid and relevant data is empty
                resp = json.dumps(
                    {
                        "loading_message": "We are searching the internet",
                        "type": "loading",
                    }
                )
                yield f"data: {resp}\n\n"
                answer = ""
                try:
                    async for chunk in websearch_stream_handler(user_question):
                        if chunk["type"] == "answer":
                            resp = json.dumps(
                                {"detail": {"answer": chunk["data"]}, "type": "data"}
                            )
                            yield f"data: {resp}\n\n"
                            answer += chunk["data"]
                        else:
                            resp = chunk.get("data", {})
                            break
                except StopIteration:
                    pass

                update_chat_history(
                    history_key, generic_chat_history, user_question, answer
                )

                result = {
                    "answer": answer,
                    "chat_id": chat_id,
                    "source": resp.get("sources"),
                }

                # await save_chat_to_db(chat_id, question, reply, answer_text, response_id,
                #                       chat_type="NORMAL", question_mode="generic")
                # await save_chat_to_db(chat_id, question, reply, answer["response"], response_id,
                #                       chat_type="NORMAL", question_mode="generic")
                chat_data = await save_chat(
                    chat_id,
                    parent,
                    question=original_question,
                    answer=answer,
                    edited=edited_question,
                    reply=reply,
                    selections=selections,
                    file_paths=file_paths,
                )
                result["id"] = chat_data.get("id")
                result["chat_id"] = chat_data.get("chat_id")
                resp = json.dumps({"detail": result, "type": "final"})
                yield f"data: {resp}\n\n"
                if json_content.get("sql_query_db1").lower() != "null":
                    cache_qa(question, answer["response"], ex=60 * 60 * 24 * 30)
                return
        # Step 4: Generate summary and prepare response
        logger.info("Generating summary...")
        resp = json.dumps({"loading_message": "Generating summary", "type": "loading"})
        yield f"data: {resp}\n\n"
        summary = ""
        end_stream_response = {}
        try:
            # summary = await generate_summary(user_question, result_db, generic_chat_history, STREAM_SUMMARY_PROMPt)
            async for chunk in summary_stream_handler(
                user_question=user_question,
                json_result=result_db,
                history=generic_chat_history,
                summary_prompt=STREAM_SUMMARY_PROMPt,
            ):
                if chunk.get("type") == "answer":
                    summary += chunk.get("data")
                    result = {"answer": chunk.get("data"), "chat_id": chat_id}
                    data_to_send = json.dumps({"detail": result, "type": "data"})
                    yield f"data: {data_to_send}\n\n"
                else:
                    end_stream_response = chunk.get("data", {})
        except StopIteration as e:
            logger.error(f"Stream ended")
            logger.info(f"Summary: {summary}")
        update_chat_history(history_key, generic_chat_history, user_question, summary)

        response = {
            "symbol": end_stream_response.get("symbol"),
            "event_ids": event_ids,
            "news_ids": news_ids,
            "metric_data": metric_json,
            "chart_type": chart_type,
            "start_date": end_stream_response.get("start_date"),
            "end_date": end_stream_response.get("end_date"),
        }
        # await save_chat_to_db(chat_id, question, reply, summary.get("answer"), response_id, **response)
        chat_data = await save_chat(
            chat_id,
            parent,
            question=original_question,
            answer=summary,
            edited=edited_question,
            reply=reply,
            selections=selections,
            file_paths=file_paths,
            **response,
        )
        logger.info("Database updated successfully.")
        logger.info(f"Response: {response}")
        response.update(
            {
                "answer": summary,
                "id": chat_data.get("id"),
                "chat_id": chat_data.get("chat_id"),
            }
        )

        rep = json.dumps({"detail": response, "type": "final"})
        yield f"data: {rep}\n\n"
        if json_content.get("sql_query_db1").lower() != "null":
            pass
        else:
            cache_qa(question, summary)
        return
    except Exception as e:
        logger.error(f"Error: {e}")
        send_alert_mail(f"Error: {e} \n {traceback.format_exc()}")
        yield f"data: {json.dumps({'error': 'Internal server error', 'type': 'error'})}\n\n"
        return


async def generic_chat_service_v3(
    db: AsyncSession,
    chat_id: str,
    account_id: str,
    trading_token: str,
    data: ChatSchema,
    generic_chat_history: list = [],
    is_websearch_enabled: bool = False,
    question: Optional[str] = None,
    reply: Optional[str] = None,
    token: str = "",
    track: str = "",
    selected_category: Optional[str] = None,
    selected_option: Optional[str] = None,
    background_tasks: BackgroundTasks = None,
    parent: str = "",
    edited_question: bool = False,
    original_question="",
    selections=None,
    file_paths=[],
):
    try:
        print("coming in here")
        history_key = f"chat_history_{chat_id}"
        chat_context_key = f"chat_context_{chat_id}"
        chat_context = cache.get(chat_context_key) or {}

        # Handle image-based chat requests
        if data.hidden_images or data.images:
            images = data.hidden_images or data.images or data.chat_images or []
            resp = json.dumps(
                {"loading_message": "We are analyzing image", "type": "loading"}
            )
            yield f"data: {resp}\n\n"
            response = chat_with_image_generic_v3(
                images, question, track, history=generic_chat_history
            )
            response = response.replace("\n", "").replace("\r", "").strip("*")
            cache.set(f"cache_images_in_chat_{chat_id}", images)
            if response.lower() == "realtime":
                resp = json.dumps(
                    {
                        "loading_message": "We are searching on the internet",
                        "type": "loading",
                    }
                )
                yield f"data: {resp}\n\n"
                await asyncio.sleep(0.3)
                # Fetch real-time data
                # realtime_data = await web_scrape_search(user_question or "", flag=True)
                realtime_data = await web_scrape_search_v3(question or "")
                # Format the response with clickable links
                response_text = realtime_data.get(
                    "answer", "Real-time data unavailable."
                )
                sources = realtime_data.get("sources", [])
                # Add formatted source links at the end of the response
                if sources:
                    response_text += "\n\nSources:\n"
                    for i, source in enumerate(sources, 1):
                        if isinstance(source, str) and source.startswith(
                            ("http://", "https://")
                        ):
                            response_text += f"[Read more]({source})"
                            if i < len(sources):
                                response_text += " | "
                final_response = response_text
            else:
                final_response = response

            # Store response in history and cache
            chat_data = await save_chat(
                chat_id,
                data.parent,
                question=data.question,
                answer=final_response,
                edited=data.edited_question,
                reply=data.reply,
                images=data.images,
                chat_images=data.chat_images,
                chat_full_images=data.hidden_images,
                logo=data.logo,
                symbol=data.symbol,
                timeframe=data.timeframe,
            )
            update_chat_history(
                history_key, generic_chat_history, original_question, final_response
            )
            logger.info(f"Returning response: {chat_id}")
            result = {
                "answer": final_response,
                "images": data.images,
                "id": chat_data.get("id"),
                "chat_id": chat_data.get("chat_id"),
            }
            resp = json.dumps(
                {
                    "detail": result,
                    "type": "final",
                }
            )
            yield f"data: {resp}\n\n"
            return

        print("version 1.1")
        resp = await get_answer_based_on_faqs(question=question, type="simple")
        if resp:
            resp = resp[0]
            if (
                "<p>Could you please clarify what you’re asking about? Provide some more context.</p>"
                not in resp
            ):
                save_chat_data = await save_chat(
                    chat_id,
                    parent,
                    question=original_question,
                    answer=resp,
                    edited=edited_question,
                    reply=reply,
                    selections=selections,
                    file_paths=file_paths,
                )
                update_chat_history(
                    history_key, generic_chat_history, original_question, resp
                )
                result = {
                    "answer": resp,
                    "chat_id": save_chat_data.get("chat_id"),
                    "id": save_chat_data.get("id"),
                }
                resp = json.dumps(
                    {
                        "detail": result,
                        "type": "final",
                    }
                )
                yield f"data: {resp}\n\n"
                return

        response = validate_user_question(question, chat_id, generic_chat_history)
        print("answer from validation: ", response)
        if response["is_realtime"]:
            question = response["new_question"]
        else:
            print("coming to here")
            save_chat_data = await save_chat(
                chat_id,
                parent,
                question=original_question,
                answer=response["answer"],
                edited=edited_question,
                reply=reply,
                selections=selections,
                file_paths=file_paths,
            )
            update_chat_history(
                history_key, generic_chat_history, original_question, response["answer"]
            )

            result = {
                "answer": response["answer"],
                "chat_id": save_chat_data.get("chat_id"),
                "id": save_chat_data.get("id"),
            }
            resp = json.dumps(
                {
                    "detail": result,
                    "type": "final",
                }
            )
            yield f"data: {resp}\n\n"
            return

        reusable_response = get_reusable_response(prompt=question)

        if reusable_response is not None:
            save_chat_data = await save_chat(
                chat_id,
                parent,
                question=original_question,
                answer=reusable_response,
                edited=edited_question,
                reply=reply,
                selections=selections,
                file_paths=file_paths,
            )
            update_chat_history(
                history_key, generic_chat_history, original_question, reusable_response
            )
            result = {
                "answer": reusable_response,
                "chat_id": save_chat_data.get("chat_id"),
                "id": save_chat_data.get("id"),
            }
            resp = json.dumps(
                {
                    "detail": result,
                    "type": "final",
                }
            )
            yield f"data: {resp}\n\n"
            return

        cached_answer = get_cached_qa(question)

        if cached_answer is not None:
            save_chat_data = await save_chat(
                chat_id,
                parent,
                question=original_question,
                answer=cached_answer,
                edited=edited_question,
                reply=reply,
                selections=selections,
                file_paths=file_paths,
            )
            update_chat_history(
                history_key, generic_chat_history, original_question, cached_answer
            )
            result = {
                "answer": cached_answer,
                "chat_id": save_chat_data.get("chat_id"),
                "id": save_chat_data.get("id"),
            }
            resp = json.dumps(
                {
                    "detail": result,
                    "type": "final",
                }
            )
            yield f"data: {resp}\n\n"
            return

        # resp = json.dumps({"loading_message": "Please wait...", "type": "loading"})
        # yield f"data: {resp}\n\n"
        user_question = (
            f"Reply to: {reply}\n\nQuestion: {question}" if reply else question
        )
        if is_websearch_enabled:
            user_question += " Please search the internet for the answer."
        logger.info(f"User question: {user_question}")
        previous_question_check = check_question_from_history_v3(
            user_question, generic_chat_history[-3:]
        )

        try:
            test_question = json.loads(previous_question_check)
        except Exception as e:
            test_question = {
                "response": "Null",
                "is_new_question": previous_question_check,
            }

        if test_question.get("response") != "Null":
            result = {"answer": test_question["response"], "chat_id": chat_id}
            # await save_chat_to_db(chat_id, question, reply, test_question["response"], response_id, chat_type="NORMAL", question_mode="generic")
            chat_data = await save_chat(
                chat_id,
                parent,
                question=original_question,
                answer=test_question["response"],
                edited=edited_question,
                reply=reply,
                selections=selections,
                file_paths=file_paths,
            )
            update_chat_history(
                history_key,
                generic_chat_history,
                original_question,
                test_question["response"],
            )
            result["id"] = chat_data.get("id")
            result["chat_id"] = chat_data.get("chat_id")
            resp = json.dumps(
                {
                    "detail": result,
                    "type": "final",
                }
            )
            yield f"data: {resp}\n\n"
            # cache_qa(question, test_question["response"])
            return

        # If is_new_question exists, use it as user_question
        if test_question.get("is_new_question") != "Null":
            user_question = test_question["is_new_question"]
        else:
            user_question = question

        # Fetch account details
        try:
            query = f"SELECT token, customer FROM {settings.DATABASE_5.split('/')[-1]}.accounts WHERE id = :account_id"
            params = {"account_id": account_id}

            result = build_metric_json_data(
                metric_execute_query(query, settings.DATABASE_5, params)
            )

            account_id = result[0]["token"]
            customer_id = str(result[0]["customer"])
        except Exception as e:
            logger.error(f"Error fetching account data: {e}")
            customer_id = account_id = ""

        decoded_customer_id = track.split("_")[0]
        logger.info(
            f"Customer ID ====>: {customer_id}, Decoded ====>: {decoded_customer_id}, Account ID ====>: {account_id}"
        )
        if selected_category:
            resp = json.dumps(
                {
                    "loading_message": f"Please wait, we're are analyzing data for {selected_category} in vuetra's database",
                    "type": "loading",
                }
            )
            yield f"data: {resp}\n\n"
            answer = ""
            try:
                async for chunk in websearch_stream_handler(user_question):
                    if chunk["type"] == "answer":
                        resp = json.dumps(
                            {"detail": {"answer": chunk["data"]}, "type": "data"}
                        )
                        yield f"data: {resp}\n\n"
                        answer += chunk["data"]
                    else:
                        resp = chunk.get("data", {})
                        break
            except StopIteration:
                pass
            update_chat_history(
                history_key, generic_chat_history, user_question, answer
            )

            result = {"answer": answer, "source": resp.get("sources")}

            chat_data = await save_chat(
                chat_id,
                parent,
                question=original_question,
                answer=answer,
                edited=edited_question,
                reply=reply,
                source=resp.get("sources"),
                selections=selections,
                file_paths=file_paths,
            )
            result["id"] = chat_data.get("id")
            result["chat_id"] = chat_data.get("chat_id")
            resp = json.dumps({"detail": result, "type": "final"})

            yield f"data: {resp}\n\n"
            cache_qa(question, answer, ex=60 * 60 * 2)
            return

        system_prompt = (
            f"- Note: Ensure using the user's ID in queries where possible. Unique ID: {account_id}"
            + first_prompt
        )

        logger.info("Calling classify_question function")
        if test_question.get("external_answer") != "Null":
            first_result = await classify_question_v3(user_question, system_prompt)
        else:
            first_result = await classify_question_v3(user_question, system_prompt)
        logger.info("classify_question function executed successfully")
        cleaned_result = (
            first_result.replace("`", "").replace("json", "").replace("\n", "")
        )
        json_content = extract_json_content(cleaned_result)
        logger.info(f"json_content: {json_content}")
        if background_tasks:
            background_tasks.add_task(prepare_dataset, user_question, json_content)
        logger.info(f"json_content: {json_content}")
        result = {}

        if json_content is None:
            update_chat_history(
                history_key, generic_chat_history, user_question, cleaned_result
            )
            result = {"answer": cleaned_result, "chat_id": chat_id}
            # await save_chat_to_db(chat_id, question,reply, result["answer"], response_id, chat_type="NORMAL", question_mode="generic")
            chat_data = await save_chat(
                chat_id,
                parent,
                question=original_question,
                answer=result["answer"],
                edited=edited_question,
                reply=reply,
                selections=selections,
                file_paths=file_paths,
            )
            result["id"] = chat_data.get("id")
            result["chat_id"] = chat_data.get("chat_id")
            resp = json.dumps(
                {
                    "detail": result,
                    "type": "final",
                }
            )
            yield f"data: {resp}\n\n"
            cache_qa(question, result["answer"], ex=60 * 60 * 24 * 30)
            return

        # Step 2: Handle specific JSON content cases
        if json_content and "answer" in json_content:
            update_chat_history(
                history_key, generic_chat_history, user_question, json_content["answer"]
            )
            result = {"answer": json_content["answer"], "chat_id": chat_id}
            # await save_chat_to_db(chat_id, question,reply, result["answer"], response_id, chat_type="NORMAL", question_mode="generic")
            chat_data = await save_chat(
                chat_id,
                parent,
                question=original_question,
                answer=result["answer"],
                edited=edited_question,
                reply=reply,
                selections=selections,
                file_paths=file_paths,
            )
            result["id"] = chat_data.get("id")
            result["chat_id"] = chat_data.get("chat_id")
            resp = json.dumps(
                {
                    "detail": result,
                    "type": "final",
                }
            )
            yield f"data: {resp}\n\n"
            cache_qa(question, result["answer"], ex=60 * 60 * 24 * 30)
            return
        if json_content:
            sql_query_db5 = json_content.get("sql_query_db5")
            if sql_query_db5 and sql_query_db5.lower() == "websocket":
                resp = json.dumps(
                    {"loading_message": "We're analyzing data", "type": "loading"}
                )
                yield f"data: {resp}\n\n"
                answer = execute(trading_token, user_question)
                update_chat_history(
                    history_key, generic_chat_history, user_question, answer
                )
                result = {"answer": answer, "chat_id": chat_id}
            sql_query_db1 = json_content.get("sql_query_db1")
            if sql_query_db1 and sql_query_db1.lower() == "trade":
                answer = await trade_question_v3(user_question, trade_prompt)
                cleaned_result = (
                    answer.replace("`", "").replace("json", "").replace("\n", "")
                )
                json_decoded_content = extract_json_content(cleaned_result)
                json_content["sql_query_db1"] = json_decoded_content.get(
                    "sql_query_db1"
                )
                json_content["chart_type"] = json_decoded_content.get("chart_type")
                if background_tasks:
                    background_tasks.add_task(
                        prepare_dataset, user_question, json_content
                    )
            sql_query_db8 = json_content.get("sql_query_db8")
            if sql_query_db8 and sql_query_db8.lower() == "fmpdata":
                resp = json.dumps(
                    {
                        "loading_message": "We're analysing financial data",
                        "type": "loading",
                    }
                )
                yield f"data: {resp}\n\n"
                answer = await fmp_execute(user_question, chat_id)
                if answer["data"]:
                    update_chat_history(
                        history_key,
                        generic_chat_history,
                        user_question,
                        answer["summary"],
                    )
                    if "chart_data" in answer.keys():
                        result = {
                            "answer": answer["summary"],
                            "chat_id": chat_id,
                            "chart_data": answer["chart_data"],
                        }
                    else:
                        result = {"answer": answer["summary"], "chat_id": chat_id}
            sql_query_db9 = json_content.get("sql_query_db9")
            if sql_query_db9 and sql_query_db9.lower() == "websearch":
                reps = json.dumps(
                    {"loading_message": "Searching the internet", "type": "loading"}
                )
                yield f"data: {reps}\n\n"
                answer = await web_scrape_search(user_question)
                update_chat_history(
                    history_key, generic_chat_history, user_question, answer["answer"]
                )
                result = {
                    "answer": answer["answer"],
                    "chat_id": chat_id,
                    "source": answer["sources"],
                }

            sql_query_db10 = json_content.get("sql_query_db10")
            if sql_query_db10 and sql_query_db10.lower() == "economicindicatordata":
                answer = await rds_sql_agent(
                    user_question, "economicindicatordata", track
                )
                update_chat_history(
                    history_key, generic_chat_history, user_question, answer["output"]
                )
                result = {"answer": answer["output"], "chat_id": chat_id}

            if result:  # nned to update if result is null here
                answer = await check_response_relevance(
                    user_question, generic_chat_history, result["answer"]
                )
                if type(answer) == dict and "sources" in answer.keys():
                    result = {
                        "answer": answer["answer"],
                        "chat_id": chat_id,
                        "source": answer["sources"],
                    }
                else:
                    result["answer"] = result.get("answer")

                update_chat_history(
                    history_key, generic_chat_history, user_question, result["answer"]
                )
                # await save_chat_to_db(chat_id, question,reply, result["answer"], response_id, chat_type="NORMAL", question_mode="generic")
                chat_data = await save_chat(
                    chat_id,
                    parent,
                    question=original_question,
                    answer=result["answer"],
                    edited=edited_question,
                    reply=reply,
                    selections=selections,
                    file_paths=file_paths,
                )
                result["id"] = chat_data.get("id")
                result["chat_id"] = chat_data.get("chat_id")
                resp = json.dumps(
                    {
                        "detail": result,
                        "type": "final",
                    }
                )
                yield f"data: {resp}\n\n"
                cache_qa(question, result["answer"], ex=60 * 60 * 2)
                return
        try:
            del json_content["sql_query_db10"]
            del json_content["sql_query_db9"]
            del json_content["sql_query_db8"]
        except:
            print("Error coming from removing checked database")
        # Step 3: Process database queries
        result_db, event_ids, news_ids, metric_json = [], {}, [], None
        chart_type = json_content.pop("chart_type", None)

        if (
            json_content.get("sql_query_db1") != "null"
            and customer_id != decoded_customer_id
        ):
            logger.error("Unauthorized trade data access attempted")
            # await save_chat_to_db(chat_id, question,reply, "You don't have any trade data.", response_id, chat_type="NORMAL", question_mode="generic")
            chat_data = await save_chat(
                chat_id,
                parent,
                question=original_question,
                answer="You don't have any trade data.",
                edited=edited_question,
                reply=reply,
                selections=selections,
                file_paths=file_paths,
            )
            result = {"answer": "You don't have any trade data."}
            result["id"] = chat_data.get("id")
            result["chat_id"] = chat_data.get("chat_id")
            resp = json.dumps(
                {
                    "detail": result,
                    "type": "final",
                }
            )
            yield f"data: {resp}\n\n"
            cache_qa(question, "You don't have any trade data.")
            return
        loading_messages = {
            "sql_query_db1": "We are gathering your trade data.",
            "sql_query_db2": "We are gathering event data.",
            "sql_query_db3": "We are gathering news data.",
        }
        for db_key, query in json_content.items():
            if query and query != "null":
                logger.info(f"Processing SQL query for {db_key}")

                # Send loading message if applicable
                if db_key in loading_messages:
                    resp = json.dumps(
                        {"loading_message": loading_messages[db_key], "type": "loading"}
                    )
                    yield f"data: {resp}\n\n"

                db_result = await process_sql_query(
                    db_key, query, account_id, DATABASE_SELECTOR
                )
                logger.info(f"DB RESULT: {db_result}")
                result_db.append(db_result)

                if db_key == "sql_query_db2":
                    event_ids = {
                        row.get("date", "").split("T")[0]: [
                            row.get("ID") for row in db_result
                        ]
                        for row in db_result
                    }
                elif db_key == "sql_query_db3":
                    news_ids = [row.get("news_id") for row in db_result]
                elif db_key in {"sql_query_db1", "sql_query_db7"}:
                    metric_json = db_result
        # search flow start
        search_queries = {
            "sql_query_db2": event_ids,
            "sql_query_db3": news_ids,
            "sql_query_db4": db_result,
            "sql_query_db6": db_result,
            "sql_query_db7": db_result,
        }

        for db_key, query in json_content.items():
            if query != "null" and not search_queries.get(
                db_key, True
            ):  # Check if query is valid and relevant data is empty
                resp = json.dumps(
                    {
                        "loading_message": "We are searching the internet",
                        "type": "loading",
                    }
                )
                yield f"data: {resp}\n\n"
                answer = ""
                try:
                    async for chunk in websearch_stream_handler(user_question):
                        if chunk["type"] == "answer":
                            resp = json.dumps(
                                {"detail": {"answer": chunk["data"]}, "type": "data"}
                            )
                            yield f"data: {resp}\n\n"
                            answer += chunk["data"]
                        else:
                            resp = chunk.get("data", {})
                            break
                except StopIteration:
                    pass

                update_chat_history(
                    history_key, generic_chat_history, user_question, answer
                )

                result = {
                    "answer": answer,
                    "chat_id": chat_id,
                    "source": resp.get("sources"),
                }

                # await save_chat_to_db(chat_id, question, reply, answer_text, response_id,
                #                       chat_type="NORMAL", question_mode="generic")
                # await save_chat_to_db(chat_id, question, reply, answer["response"], response_id,
                #                       chat_type="NORMAL", question_mode="generic")
                chat_data = await save_chat(
                    chat_id,
                    parent,
                    question=original_question,
                    answer=answer,
                    edited=edited_question,
                    reply=reply,
                    selections=selections,
                    file_paths=file_paths,
                )
                result["id"] = chat_data.get("id")
                result["chat_id"] = chat_data.get("chat_id")
                resp = json.dumps({"detail": result, "type": "final"})
                yield f"data: {resp}\n\n"
                if json_content.get("sql_query_db1").lower() != "null":
                    cache_qa(question, answer["response"], ex=60 * 60 * 24 * 30)
                return
        # Step 4: Generate summary and prepare response
        logger.info("Generating summary...")
        resp = json.dumps({"loading_message": "Generating summary", "type": "loading"})
        yield f"data: {resp}\n\n"
        summary = ""
        end_stream_response = {}
        try:
            # summary = await generate_summary(user_question, result_db, generic_chat_history, STREAM_SUMMARY_PROMPt)
            async for chunk in summary_stream_handler(
                user_question=user_question,
                json_result=result_db,
                history=generic_chat_history,
                summary_prompt=STREAM_SUMMARY_PROMPt,
            ):
                if chunk.get("type") == "answer":
                    summary += chunk.get("data")
                    result = {"answer": chunk.get("data"), "chat_id": chat_id}
                    data_to_send = json.dumps({"detail": result, "type": "data"})
                    yield f"data: {data_to_send}\n\n"
                else:
                    end_stream_response = chunk.get("data", {})
        except StopIteration as e:
            logger.error(f"Stream ended")
            logger.info(f"Summary: {summary}")
        update_chat_history(history_key, generic_chat_history, user_question, summary)

        response = {
            "symbol": end_stream_response.get("symbol"),
            "event_ids": event_ids,
            "news_ids": news_ids,
            "metric_data": metric_json,
            "chart_type": chart_type,
            "start_date": end_stream_response.get("start_date"),
            "end_date": end_stream_response.get("end_date"),
        }
        # await save_chat_to_db(chat_id, question, reply, summary.get("answer"), response_id, **response)
        chat_data = await save_chat(
            chat_id,
            parent,
            question=original_question,
            answer=summary,
            edited=edited_question,
            reply=reply,
            selections=selections,
            file_paths=file_paths,
            **response,
        )
        logger.info("Database updated successfully.")
        logger.info(f"Response: {response}")
        response.update(
            {
                "answer": summary,
                "id": chat_data.get("id"),
                "chat_id": chat_data.get("chat_id"),
            }
        )

        rep = json.dumps({"detail": response, "type": "final"})
        yield f"data: {rep}\n\n"
        if json_content.get("sql_query_db1").lower() != "null":
            pass
        else:
            cache_qa(question, summary)
        return
    except Exception as e:
        logger.error(f"Error: {e}")
        send_alert_mail(f"Error: {e} \n {traceback.format_exc()}")
        yield f"data: {json.dumps({'error': 'Internal server error', 'type': 'error'})}\n\n"
        return

async def streaming_drawing_chat_service_v3(
        db: AsyncSession,
        chat_id: str,
        account_id: str,
        drawing_id: str,
        data: DrawingToolChatSchema,
        generic_chat_history: list = [],
        question: Optional[str] = None,
        track: str = "",
        parent: str = "",
        original_question="",
):
    try:
        history_key = f"chat_history_{chat_id}"
        resp = json.dumps(
            {
                "loading_message": "We're analyzing your trading data",  # Fixed typo
                "type": "loading",
            }
        )
        yield f"data: {resp}\n\n"
        if drawing_id:
            cache.set_user_cache(f"chat_context_drawing_{chat_id}", {"drawing_id": drawing_id})

        resp = await get_answer_based_on_faqs(question=question, type="simple")
        if resp:
            resp = resp[0]
            if ("<p>Could you please clarify what you’re asking about? Provide some more context.</p>" not in resp):
                save_chat_data = await save_chat(
                    chat_id,
                    parent,
                    question=original_question,
                    answer=resp,
                )
                update_chat_history(history_key, generic_chat_history, question, resp)
                result = {
                    "answer": resp,
                    "chat_id": save_chat_data.get("chat_id"),
                    "id": save_chat_data.get("id"),
                }
                resp = json.dumps(
                    {
                        "detail": result,
                        "type": "final",
                    }
                )
                yield f"data: {resp}\n\n"
                return

        if not drawing_id:
            chat_id_data = cache.get(f"chat_context_drawing_{chat_id}") or {}
            print(chat_id_data)
            if chat_id_data:
                drawing_id = chat_id_data.get("drawing_id")

        metadata = cache.get(f"context_drawing_tool_{track}_{drawing_id}") or {}
        print(metadata)

        def _clean_html_response(response: str) -> str:
            """Clean and validate HTML response"""
            # Remove any markdown formatting that might have slipped through
            response = response.replace('**', '<strong>').replace('**', '</strong>')
            response = response.replace('*', '<em>').replace('*', '</em>')

            # Ensure proper HTML structure
            if not response.startswith('<'):
                response = f'<p>{response}</p>'

            return response

        metadata_json = json.dumps(metadata, indent=4) if metadata else "{}"
        metadata_json = metadata_json.replace("{", "{{").replace("}", "}}")
        #
        if generic_chat_history:
            conversation_string = "conversation:\n"
            for chat in generic_chat_history:
                conversation_string += f"Question: {chat[0]}\nAnswer: {chat[1]}\n\n"
        else:
            conversation_string = ""

        # Fixed prompt - using direct string formatting instead of PromptTemplate
        analysis_drawing_tool_prompt = f'''
            You are a Professional Financial Manager and Trading Analysis Expert. Your role is to provide accurate, helpful financial answer based on the provided trading data.

            IMPORTANT INSTRUCTIONS:
            1. You must ONLY use the provided metadata for analysis - do not make up or assume any data
            2. Always respond in clean HTML format suitable for innerHTML implementation
            3. Handle single response - no follow-up questions needed
            4. Be professional, concise, and actionable in your responses

            METADATA PROVIDED:
            {metadata_json}

            CONVERSATION HISTORY:
            {conversation_string}

            CURRENT USER QUESTION:
            {question}

            RESPONSE GUIDELINES:
            
            **SCENARIO 1 - OUTSIDE SCOPE (NEWS/EVENTS/EXTERNAL):**
            - return response = <p>external</p>
            
            **SCENARIO 2 - QUESTION NOT ANSWERABLE FROM METADATA:**
            - return response = <p>external</p>
            
            **SCENARIO 3 - CLEAR & ANSWERABLE QUESTION:**
            - Provide short and consice answer using the metadata
            - Include specific requirement like data points, indicators, and actionable insights
            - Use proper HTML formatting with headings, lists, and emphasis

            **SCENARIO 4 - VAGUE/UNCLEAR QUESTION:**
            - If the question is vague or unclear, use conversation history to understand context
            - Reformulate the question based on available metadata and provide the best possible short and consice answer
            - Start with: "Based on our conversation and the available data, I understand you're asking about..."

            HTML FORMATTING REQUIREMENTS:
            - Use proper HTML tags: <h2>, <h3>, <p>, <ul>, <li>, <strong>, <em>
            - Use color coding: <span style="color: #e74c3c">red for bearish</span>, <span style="color: #27ae60">green for bullish</span>
            - Include proper spacing with <br> tags where needed
            - Make key insights bold with <strong> tags
            - Use bullet points for lists of recommendations

            RESPONSE STRUCTURE:
            1. Short and concise answer of the question
            2. Main analysis/answer based on metadata
            4. Any relevant warnings or disclaimers

            Remember:
            - Stay within your role as a Financial Manager
            - Only use provided metadata
            - Be professional and helpful
            - Format response for direct innerHTML usage

            Your response:
        '''

        llm = get_openai_model_v3()
        response = llm.predict(analysis_drawing_tool_prompt)
        answer = _clean_html_response(response)
        answer = answer.replace("\n", "").strip()
        print(answer)
        if "external" not in answer:
            save_chat_data = await save_chat(
                chat_id,
                parent,
                question=original_question,
                answer=answer
            )

            update_chat_history(
                history_key, generic_chat_history, original_question, answer
            )

            result = {
                "answer": answer,
                "chat_id": save_chat_data.get("chat_id"),
                "id": save_chat_data.get("id"),
            }

            resp = json.dumps(
                {
                    "detail": result,
                    "type": "final",
                }
            )

            yield f"data: {resp}\n\n"
            return

        question = question + f"symbol={metadata['metadata']['symbol']}"
        reusable_response = get_reusable_response(prompt=question)

        if reusable_response is not None:
            save_chat_data = await save_chat(
                chat_id,
                parent,
                question=original_question,
                answer=reusable_response,
            )
            update_chat_history(
                history_key, generic_chat_history, original_question, reusable_response
            )
            result = {
                "answer": reusable_response,
                "chat_id": save_chat_data.get("chat_id"),
                "id": save_chat_data.get("id"),
            }
            resp = json.dumps(
                {
                    "detail": result,
                    "type": "final",
                }
            )
            yield f"data: {resp}\n\n"
            return

        cached_answer = get_cached_qa(question)

        if cached_answer is not None:
            save_chat_data = await save_chat(
                chat_id,
                parent,
                question=original_question,
                answer=cached_answer
            )
            update_chat_history(
                history_key, generic_chat_history, original_question, cached_answer
            )
            result = {
                "answer": cached_answer,
                "chat_id": save_chat_data.get("chat_id"),
                "id": save_chat_data.get("id"),
            }
            resp = json.dumps(
                {
                    "detail": result,
                    "type": "final",
                }
            )
            yield f"data: {resp}\n\n"
            return

        # resp = json.dumps({"loading_message": "Please wait...", "type": "loading"})
        # yield f"data: {resp}\n\n"
        user_question = question
        logger.info(f"User question: {user_question}")
        previous_question_check = check_question_from_history_v3(
            user_question, generic_chat_history[-3:]
        )

        try:
            test_question = json.loads(previous_question_check)
        except Exception as e:
            test_question = {
                "response": "Null",
                "is_new_question": previous_question_check,
            }

        if test_question.get("response") != "Null":
            result = {"answer": test_question["response"], "chat_id": chat_id}
            # await save_chat_to_db(chat_id, question, reply, test_question["response"], response_id, chat_type="NORMAL", question_mode="generic")
            chat_data = await save_chat(
                chat_id,
                parent,
                question=original_question,
                answer=test_question["response"]
            )
            update_chat_history(
                history_key,
                generic_chat_history,
                original_question,
                test_question["response"],
            )
            result["id"] = chat_data.get("id")
            result["chat_id"] = chat_data.get("chat_id")
            resp = json.dumps(
                {
                    "detail": result,
                    "type": "final",
                }
            )
            yield f"data: {resp}\n\n"
            # cache_qa(question, test_question["response"])
            return

        # If is_new_question exists, use it as user_question
        if test_question.get("is_new_question") != "Null":
            user_question = test_question["is_new_question"]
        else:
            user_question = question

        # Fetch account details
        try:
            query = f"SELECT token, customer FROM {settings.DATABASE_5.split('/')[-1]}.accounts WHERE id = :account_id"
            params = {"account_id": account_id}

            result = build_metric_json_data(
                metric_execute_query(query, settings.DATABASE_5, params)
            )

            account_id = result[0]["token"]
            customer_id = str(result[0]["customer"])
        except Exception as e:
            logger.error(f"Error fetching account data: {e}")
            customer_id = account_id = ""

        decoded_customer_id = track.split("_")[0]
        logger.info(
            f"Customer ID ====>: {customer_id}, Decoded ====>: {decoded_customer_id}, Account ID ====>: {account_id}"
        )

        system_prompt = (
                f"- Note: Ensure using the user's ID in queries where possible. Unique ID: {account_id}"
                + first_prompt
        )

        logger.info("Calling classify_question function")
        if test_question.get("external_answer") != "Null":
            first_result = await classify_question_v3(user_question, system_prompt)
        else:
            first_result = await classify_question_v3(user_question, system_prompt)
        logger.info("classify_question function executed successfully")
        cleaned_result = (
            first_result.replace("`", "").replace("json", "").replace("\n", "")
        )
        json_content = extract_json_content(cleaned_result)
        logger.info(f"json_content: {json_content}")
        result = {}

        if json_content is None:
            update_chat_history(
                history_key, generic_chat_history, user_question, cleaned_result
            )
            result = {"answer": cleaned_result, "chat_id": chat_id}
            # await save_chat_to_db(chat_id, question,reply, result["answer"], response_id, chat_type="NORMAL", question_mode="generic")
            chat_data = await save_chat(
                chat_id,
                parent,
                question=original_question,
                answer=result["answer"]
            )
            result["id"] = chat_data.get("id")
            result["chat_id"] = chat_data.get("chat_id")
            resp = json.dumps(
                {
                    "detail": result,
                    "type": "final",
                }
            )
            yield f"data: {resp}\n\n"
            cache_qa(question, result["answer"], ex=60 * 60 * 24 * 30)
            return

        # Step 2: Handle specific JSON content cases
        if json_content and "answer" in json_content:
            update_chat_history(
                history_key, generic_chat_history, user_question, json_content["answer"]
            )
            result = {"answer": json_content["answer"], "chat_id": chat_id}
            # await save_chat_to_db(chat_id, question,reply, result["answer"], response_id, chat_type="NORMAL", question_mode="generic")
            chat_data = await save_chat(
                chat_id,
                parent,
                question=original_question,
                answer=result["answer"]
            )
            result["id"] = chat_data.get("id")
            result["chat_id"] = chat_data.get("chat_id")
            resp = json.dumps(
                {
                    "detail": result,
                    "type": "final",
                }
            )
            yield f"data: {resp}\n\n"
            cache_qa(question, result["answer"], ex=60 * 60 * 24 * 30)
            return
        if json_content:
            sql_query_db5 = json_content.get("sql_query_db5")
            if sql_query_db5 and sql_query_db5.lower() == "websocket":
                resp = json.dumps(
                    {"loading_message": "We're analyzing data", "type": "loading"}
                )
            sql_query_db1 = json_content.get("sql_query_db1")
            if sql_query_db1 and sql_query_db1.lower() == "trade":
                answer = await trade_question_v3(user_question, trade_prompt)
                cleaned_result = (
                    answer.replace("`", "").replace("json", "").replace("\n", "")
                )
                json_decoded_content = extract_json_content(cleaned_result)
                json_content["sql_query_db1"] = json_decoded_content.get(
                    "sql_query_db1"
                )
                json_content["chart_type"] = json_decoded_content.get("chart_type")
            sql_query_db8 = json_content.get("sql_query_db8")
            if sql_query_db8 and sql_query_db8.lower() == "fmpdata":
                resp = json.dumps(
                    {
                        "loading_message": "We're analysing financial data",
                        "type": "loading",
                    }
                )
                yield f"data: {resp}\n\n"
                answer = await fmp_execute(user_question, chat_id)
                print("answer from fmp_execute: ", answer)
                if answer["data"] and "Unable to generate" not in answer["summary"]:
                    update_chat_history(
                        history_key,
                        generic_chat_history,
                        user_question,
                        answer["summary"],
                    )
                    if "chart_data" in answer.keys():
                        result = {
                            "answer": answer["summary"],
                            "chat_id": chat_id,
                            "chart_data": answer["chart_data"],
                        }
                    else:
                        result = {"answer": answer["summary"], "chat_id": chat_id}
            sql_query_db9 = json_content.get("sql_query_db9")
            if sql_query_db9 and sql_query_db9.lower() == "websearch":
                reps = json.dumps(
                    {"loading_message": "Searching the internet", "type": "loading"}
                )
                yield f"data: {reps}\n\n"
                answer = await web_scrape_search(user_question)
                update_chat_history(
                    history_key, generic_chat_history, user_question, answer["answer"]
                )
                result = {
                    "answer": answer["answer"],
                    "chat_id": chat_id,
                    "source": answer["sources"],
                }

            sql_query_db10 = json_content.get("sql_query_db10")
            if sql_query_db10 and sql_query_db10.lower() == "economicindicatordata":
                answer = await rds_sql_agent(
                    user_question, "economicindicatordata", track
                )
                update_chat_history(
                    history_key, generic_chat_history, user_question, answer["output"]
                )
                result = {"answer": answer["output"], "chat_id": chat_id}

            if result:  # nned to update if result is null here
                answer = await check_response_relevance(
                    user_question, generic_chat_history, result["answer"]
                )
                if type(answer) == dict and "sources" in answer.keys():
                    result = {
                        "answer": answer["answer"],
                        "chat_id": chat_id,
                        "source": answer["sources"],
                    }
                else:
                    result["answer"] = result.get("answer")

                update_chat_history(
                    history_key, generic_chat_history, user_question, result["answer"]
                )
                # await save_chat_to_db(chat_id, question,reply, result["answer"], response_id, chat_type="NORMAL", question_mode="generic")
                chat_data = await save_chat(
                    chat_id,
                    parent,
                    question=original_question,
                    answer=result["answer"]
                )
                result["id"] = chat_data.get("id")
                result["chat_id"] = chat_data.get("chat_id")
                resp = json.dumps(
                    {
                        "detail": result,
                        "type": "final",
                    }
                )
                yield f"data: {resp}\n\n"
                cache_qa(question, result["answer"], ex=60 * 60 * 2)
                return
        # Step 3: Process database queries
        result_db, event_ids, news_ids, metric_json = [], {}, [], None
        chart_type = json_content.pop("chart_type", None)
        try:
            del json_content["sql_query_db10"]
            del json_content["sql_query_db9"]
            del json_content["sql_query_db8"]
        except:
            print("Error coming from removing checked database")
        if (
                json_content.get("sql_query_db1") != "null"
                and customer_id != decoded_customer_id
        ):
            logger.error("Unauthorized trade data access attempted")
            # await save_chat_to_db(chat_id, question,reply, "You don't have any trade data.", response_id, chat_type="NORMAL", question_mode="generic")
            chat_data = await save_chat(
                chat_id,
                parent,
                question=original_question,
                answer="You don't have any trade data."
            )
            result = {"answer": "You don't have any trade data."}
            result["id"] = chat_data.get("id")
            result["chat_id"] = chat_data.get("chat_id")
            resp = json.dumps(
                {
                    "detail": result,
                    "type": "final",
                }
            )
            yield f"data: {resp}\n\n"
            cache_qa(question, "You don't have any trade data.")
            return
        loading_messages = {
            "sql_query_db1": "We are gathering your trade data.",
            "sql_query_db2": "We are gathering event data.",
            "sql_query_db3": "We are gathering news data.",
        }
        db_result = []
        for db_key, query in json_content.items():
            if query and query != "null":
                logger.info(f"Processing SQL query for {db_key}")

                # Send loading message if applicable
                if db_key in loading_messages:
                    resp = json.dumps(
                        {"loading_message": loading_messages[db_key], "type": "loading"}
                    )
                    yield f"data: {resp}\n\n"

                db_result = await process_sql_query(
                    db_key, query, account_id, DATABASE_SELECTOR
                )
                logger.info(f"DB RESULT: {db_result}")
                result_db.append(db_result)

                if db_key == "sql_query_db2":
                    event_ids = {
                        row.get("date", "").split("T")[0]: [
                            row.get("ID") for row in db_result
                        ]
                        for row in db_result
                    }
                elif db_key == "sql_query_db3":
                    news_ids = [row.get("news_id") for row in db_result]
                elif db_key in {"sql_query_db1", "sql_query_db7"}:
                    metric_json = db_result
        if db_result:
            # search flow start
            search_queries = {
                "sql_query_db2": event_ids,
                "sql_query_db3": news_ids,
                "sql_query_db4": db_result,
                "sql_query_db6": db_result,
                "sql_query_db7": db_result,
            }
        else:
            search_queries = {
                "sql_query_db2": event_ids,
                "sql_query_db3": news_ids,
            }

        for db_key, query in json_content.items():
            if query != "null" and not search_queries.get(
                    db_key, True
            ):  # Check if query is valid and relevant data is empty
                resp = json.dumps(
                    {
                        "loading_message": "We are searching the internet",
                        "type": "loading",
                    }
                )
                yield f"data: {resp}\n\n"
                answer = ""
                try:
                    async for chunk in websearch_stream_handler(user_question):
                        if chunk["type"] == "answer":
                            resp = json.dumps(
                                {"detail": {"answer": chunk["data"]}, "type": "data"}
                            )
                            yield f"data: {resp}\n\n"
                            answer += chunk["data"]
                        else:
                            resp = chunk.get("data", {})
                            break
                except StopIteration:
                    pass

                update_chat_history(
                    history_key, generic_chat_history, user_question, answer
                )

                result = {
                    "answer": answer,
                    "chat_id": chat_id,
                    "source": resp.get("sources"),
                }

                # await save_chat_to_db(chat_id, question, reply, answer_text, response_id,
                #                       chat_type="NORMAL", question_mode="generic")
                # await save_chat_to_db(chat_id, question, reply, answer["response"], response_id,
                #                       chat_type="NORMAL", question_mode="generic")
                chat_data = await save_chat(
                    chat_id,
                    parent,
                    question=original_question,
                    answer=answer
                )
                result["id"] = chat_data.get("id")
                result["chat_id"] = chat_data.get("chat_id")
                resp = json.dumps({"detail": result, "type": "final"})
                yield f"data: {resp}\n\n"
                if json_content.get("sql_query_db1").lower() != "null":
                    cache_qa(question, answer["response"], ex=60 * 60 * 24 * 30)
                return
        # Step 4: Generate summary and prepare response
        logger.info("Generating summary...")
        resp = json.dumps({"loading_message": "Generating summary", "type": "loading"})
        yield f"data: {resp}\n\n"
        summary = ""
        end_stream_response = {}
        try:
            # summary = await generate_summary(user_question, result_db, generic_chat_history, STREAM_SUMMARY_PROMPt)
            async for chunk in summary_stream_handler(
                    user_question=user_question,
                    json_result=result_db,
                    history=generic_chat_history,
                    summary_prompt=STREAM_SUMMARY_PROMPt,
            ):
                if chunk.get("type") == "answer":
                    summary += chunk.get("data")
                    result = {"answer": chunk.get("data"), "chat_id": chat_id}
                    data_to_send = json.dumps({"detail": result, "type": "data"})
                    yield f"data: {data_to_send}\n\n"
                else:
                    end_stream_response = chunk.get("data", {})
        except StopIteration as e:
            logger.error(f"Stream ended")
            logger.info(f"Summary: {summary}")
        update_chat_history(history_key, generic_chat_history, user_question, summary)

        response = {
            "symbol": end_stream_response.get("symbol"),
            "event_ids": event_ids,
            "news_ids": news_ids,
            "metric_data": metric_json,
            "chart_type": chart_type,
            "start_date": end_stream_response.get("start_date"),
            "end_date": end_stream_response.get("end_date"),
        }
        # await save_chat_to_db(chat_id, question, reply, summary.get("answer"), response_id, **response)
        chat_data = await save_chat(
            chat_id,
            parent,
            question=original_question,
            answer=summary,
            **response,
        )
        logger.info("Database updated successfully.")
        logger.info(f"Response: {response}")
        response.update(
            {
                "answer": summary,
                "id": chat_data.get("id"),
                "chat_id": chat_data.get("chat_id"),
            }
        )

        rep = json.dumps({"detail": response, "type": "final"})
        yield f"data: {rep}\n\n"
        if json_content.get("sql_query_db1").lower() != "null":
            pass
        else:
            cache_qa(question, summary)
        return
    except Exception as e:
        logger.error(f"Error: {e}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        yield f"data: {json.dumps({'error': 'Internal server error', 'type': 'error'})}\n\n"
        return


async def generate_drawing_instuaction(
        db: AsyncSession,
        chat_id: str,
        account_id: str,
        drawing_id: str,
        data: DrawingToolChatSchema,
        generic_chat_history: list = [],
        question: Optional[str] = None,
        track: str = "",
        parent: str = "",
        original_question="",
):
    try:
        history_key = f"chat_history_{chat_id}"
        resp = json.dumps(
            {
                "loading_message": "We're analyzing your data",  # Fixed typo
                "type": "loading",
            }
        )
        yield f"data: {resp}\n\n"

        if drawing_id:
            cache.set_user_cache(f"chat_context_drawing_{chat_id}", {"drawing_id": drawing_id})

        if not drawing_id:
            chat_id_data = cache.get(f"chat_context_drawing_{chat_id}") or {}
            if chat_id_data:
                drawing_id = chat_id_data.get("drawing_id")

        metadata_caching = cache.get(f"context_drawing_tool_{track}_{drawing_id}") or {}
        metadata = metadata_caching["metadata"]
        client_openai = get_antropi_openai_client()
        agent = TechnicalAnalysisAgent(client_openai)

        indicators = agent.analyze_query(question)
        if not indicators:
            answer = "Could not identify indicators from query so please add your exact requirement"
            save_chat_data = await save_chat(
                chat_id,
                parent,
                question=original_question,
                answer=answer,
            )

            update_chat_history(
                history_key, generic_chat_history, original_question, answer
            )

            result = {
                "answer": answer,
                "chat_id": save_chat_data.get("chat_id"),
                "id": save_chat_data.get("id"),
            }

            resp = json.dumps(
                {
                    "detail": result,
                    "type": "final",
                }
            )
            yield f"data: {resp}\n\n"
            return
        # Step 2: Load calculation knowledge

        knowledge = agent.load_calculation_knowledge(indicators)

        # Step 3: Perform analysis
        analysis_result = agent.perform_analysis(question, metadata, indicators, knowledge)

        answer = analysis_result.get("explanation", "")
        save_chat_data = await save_chat(
            chat_id,
            parent,
            question=original_question,
            answer=answer,
            drawing_instructions=analysis_result.get("drawing_instructions", {})
        )

        update_chat_history(
            history_key, generic_chat_history, original_question, answer
        )

        result = {
            "answer": answer,
            "drawing_instructions": analysis_result.get("drawing_instructions", {}),
            "chat_id": save_chat_data.get("chat_id"),
            "id": save_chat_data.get("id"),
        }
        cache.set_user_cache(f"chat_drawing_context_{chat_id}", {"drawing_id": drawing_id})

        resp = json.dumps(
            {
                "detail": result,
                "type": "final",
            }
        )
        yield f"data: {resp}\n\n"
        return

    except Exception as e:
        logger.error(f"Error: {e}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        yield f"data: {json.dumps({'error': 'Internal server error', 'type': 'error'})}\n\n"
        return

