import json
import re
import os
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, asdict
from collections import defaultdict
import asyncio

from app.utils.logger import generic_logger as logger

@dataclass
class CostAnalysisRecord:
    """Structured cost analysis record from logs"""
    request_id: str
    endpoint: str
    flow_type: str
    customer_id: str
    account_id: str
    chat_id: str
    question: str
    request_duration_ms: float
    total_cost: float
    total_input_tokens: int
    total_output_tokens: int
    total_tokens: int
    timestamp: datetime
    model_usages: List[Dict[str, Any]]
    web_search_calls: int = 0
    web_search_cost: float = 0.0

@dataclass
class DashboardStats:
    """Dashboard statistics"""
    total_requests: int
    total_cost: float
    total_tokens: int
    avg_cost_per_request: float
    avg_tokens_per_request: float
    avg_duration_ms: float
    top_models: List[Dict[str, Any]]
    top_flows: List[Dict[str, Any]]
    cost_by_hour: List[Dict[str, Any]]
    recent_requests: List[Dict[str, Any]]

class CostDashboardService:
    """Service to parse logs and provide dashboard data"""

    def __init__(self, log_file_path: str = "logs/generic.log"):
        self.log_file_path = log_file_path
        self.cost_records: List[CostAnalysisRecord] = []

    def parse_cost_analysis_from_logs(self, hours_back: int = 24) -> List[CostAnalysisRecord]:
        """Parse cost analysis reports from log files"""
        records = []

        try:
            if not os.path.exists(self.log_file_path):
                logger.warning(f"Log file not found: {self.log_file_path}")
                return records

            with open(self.log_file_path, 'r') as file:
                content = file.read()

            # Find all cost analysis reports
            pattern = r'COMPREHENSIVE COST ANALYSIS REPORT.*?={80,}'
            reports = re.findall(pattern, content, re.DOTALL)

            cutoff_time = datetime.now() - timedelta(hours=hours_back)

            for report in reports:
                try:
                    record = self._parse_single_report(report)
                    if record and record.timestamp >= cutoff_time:
                        records.append(record)
                except Exception as e:
                    logger.error(f"Error parsing report: {e}")
                    continue

        except Exception as e:
            logger.error(f"Error reading log file: {e}")

        return sorted(records, key=lambda x: x.timestamp, reverse=True)

    def _parse_single_report(self, report: str) -> Optional[CostAnalysisRecord]:
        """Parse a single cost analysis report"""
        try:
            # Extract basic info
            request_id = self._extract_field(report, r'Request ID: (.+)')
            endpoint = self._extract_field(report, r'Endpoint: (.+)')
            flow_type = self._extract_field(report, r'Flow Type: (.+)')
            customer_id = self._extract_field(report, r'Customer ID: (.+)')
            account_id = self._extract_field(report, r'Account ID: (.+)')
            chat_id = self._extract_field(report, r'Chat ID: (.+)')
            question = self._extract_field(report, r'Question: (.+?)\.\.\.') or self._extract_field(report, r'Question: (.+)')

            # Extract metrics
            duration_str = self._extract_field(report, r'Request Duration: ([\d.]+)ms')
            duration_ms = float(duration_str) if duration_str else 0.0

            cost_str = self._extract_field(report, r'TOTAL REQUEST COST: \$?([\d.]+)')
            total_cost = float(cost_str) if cost_str else 0.0

            tokens_str = self._extract_field(report, r'TOTAL TOKENS: ([\d,]+)')
            total_tokens = int(tokens_str.replace(',', '')) if tokens_str else 0

            input_tokens_str = self._extract_field(report, r'TOTAL INPUT TOKENS: ([\d,]+)')
            total_input_tokens = int(input_tokens_str.replace(',', '')) if input_tokens_str else 0

            output_tokens_str = self._extract_field(report, r'TOTAL OUTPUT TOKENS: ([\d,]+)')
            total_output_tokens = int(output_tokens_str.replace(',', '')) if output_tokens_str else 0

            # Extract web search info
            web_calls_str = self._extract_field(report, r'Web Search Calls: (\d+)')
            web_search_calls = int(web_calls_str) if web_calls_str else 0

            web_cost_str = self._extract_field(report, r'Web Search Cost: \$?([\d.]+)')
            web_search_cost = float(web_cost_str) if web_cost_str else 0.0

            # Extract model usages
            model_usages = self._extract_model_usages(report)

            # Extract timestamp from log line (approximate)
            timestamp = datetime.now()  # For now, use current time

            return CostAnalysisRecord(
                request_id=request_id or "unknown",
                endpoint=endpoint or "unknown",
                flow_type=flow_type or "unknown",
                customer_id=customer_id or "unknown",
                account_id=account_id or "",
                chat_id=chat_id or "",
                question=question or "unknown",
                request_duration_ms=duration_ms,
                total_cost=total_cost,
                total_input_tokens=total_input_tokens,
                total_output_tokens=total_output_tokens,
                total_tokens=total_tokens,
                timestamp=timestamp,
                model_usages=model_usages,
                web_search_calls=web_search_calls,
                web_search_cost=web_search_cost
            )

        except Exception as e:
            logger.error(f"Error parsing single report: {e}")
            return None

    def _extract_field(self, text: str, pattern: str) -> Optional[str]:
        """Extract a field using regex pattern"""
        match = re.search(pattern, text)
        return match.group(1).strip() if match else None

    def _extract_model_usages(self, report: str) -> List[Dict[str, Any]]:
        """Extract model usage details from report"""
        model_usages = []

        # Pattern to match model sections
        model_pattern = r'Model: (.+?)\n.*?API Calls: (\d+)\n.*?Functions: (.+?)\n.*?Input Tokens: ([\d,]+)\n.*?Output Tokens: ([\d,]+)\n.*?Total Cost: \$?([\d.]+)'

        matches = re.findall(model_pattern, report, re.DOTALL)

        for match in matches:
            model_name, calls, functions, input_tokens, output_tokens, cost = match
            model_usages.append({
                "model_name": model_name.strip(),
                "api_calls": int(calls),
                "functions": [f.strip() for f in functions.split(',')],
                "input_tokens": int(input_tokens.replace(',', '')),
                "output_tokens": int(output_tokens.replace(',', '')),
                "total_cost": float(cost)
            })

        return model_usages

    def generate_dashboard_stats(self, records: List[CostAnalysisRecord]) -> DashboardStats:
        """Generate dashboard statistics from cost records"""
        if not records:
            return DashboardStats(
                total_requests=0,
                total_cost=0.0,
                total_tokens=0,
                avg_cost_per_request=0.0,
                avg_tokens_per_request=0.0,
                avg_duration_ms=0.0,
                top_models=[],
                top_flows=[],
                cost_by_hour=[],
                recent_requests=[]
            )

        # Basic stats
        total_requests = len(records)
        total_cost = sum(r.total_cost for r in records)
        total_tokens = sum(r.total_tokens for r in records)
        avg_cost_per_request = total_cost / total_requests if total_requests > 0 else 0
        avg_tokens_per_request = total_tokens / total_requests if total_requests > 0 else 0
        avg_duration_ms = sum(r.request_duration_ms for r in records) / total_requests if total_requests > 0 else 0

        # Model usage analysis
        model_stats = defaultdict(lambda: {"calls": 0, "cost": 0.0, "tokens": 0})
        for record in records:
            for usage in record.model_usages:
                model_name = usage["model_name"]
                model_stats[model_name]["calls"] += usage["api_calls"]
                model_stats[model_name]["cost"] += usage["total_cost"]
                model_stats[model_name]["tokens"] += usage["input_tokens"] + usage["output_tokens"]

        top_models = sorted(
            [{"model": k, **v} for k, v in model_stats.items()],
            key=lambda x: x["cost"],
            reverse=True
        )[:10]

        # Flow type analysis
        flow_stats = defaultdict(lambda: {"requests": 0, "cost": 0.0, "avg_duration": 0.0})
        for record in records:
            flow_stats[record.flow_type]["requests"] += 1
            flow_stats[record.flow_type]["cost"] += record.total_cost
            flow_stats[record.flow_type]["avg_duration"] += record.request_duration_ms

        for flow in flow_stats.values():
            if flow["requests"] > 0:
                flow["avg_duration"] = flow["avg_duration"] / flow["requests"]

        top_flows = sorted(
            [{"flow": k, **v} for k, v in flow_stats.items()],
            key=lambda x: x["cost"],
            reverse=True
        )

        # Hourly cost breakdown
        hourly_stats = defaultdict(lambda: {"cost": 0.0, "requests": 0})
        for record in records:
            hour_key = record.timestamp.strftime("%Y-%m-%d %H:00")
            hourly_stats[hour_key]["cost"] += record.total_cost
            hourly_stats[hour_key]["requests"] += 1

        cost_by_hour = [
            {"hour": k, **v} for k, v in sorted(hourly_stats.items())
        ][-24:]  # Last 24 hours

        # Recent requests
        recent_requests = [
            {
                "request_id": r.request_id,
                "endpoint": r.endpoint,
                "flow_type": r.flow_type,
                "question": r.question[:100] + "..." if len(r.question) > 100 else r.question,
                "total_cost": r.total_cost,
                "total_tokens": r.total_tokens,
                "duration_ms": r.request_duration_ms,
                "timestamp": r.timestamp.isoformat(),
                "models_used": [usage["model_name"] for usage in r.model_usages]
            }
            for r in records[:20]  # Last 20 requests
        ]

        return DashboardStats(
            total_requests=total_requests,
            total_cost=total_cost,
            total_tokens=total_tokens,
            avg_cost_per_request=avg_cost_per_request,
            avg_tokens_per_request=avg_tokens_per_request,
            avg_duration_ms=avg_duration_ms,
            top_models=top_models,
            top_flows=top_flows,
            cost_by_hour=cost_by_hour,
            recent_requests=recent_requests
        )

    async def get_dashboard_data(self, hours_back: int = 24) -> Dict[str, Any]:
        """Get complete dashboard data"""
        records = self.parse_cost_analysis_from_logs(hours_back)
        stats = self.generate_dashboard_stats(records)

        return {
            "stats": asdict(stats),
            "last_updated": datetime.now().isoformat(),
            "time_range_hours": hours_back,
            "total_records_found": len(records)
        }

    def get_dashboard_data_sync(self, hours_back: int = 24) -> Dict[str, Any]:
        """Get complete dashboard data (synchronous version for Streamlit)"""
        records = self.parse_cost_analysis_from_logs(hours_back)
        stats = self.generate_dashboard_stats(records)

        return {
            "stats": asdict(stats),
            "last_updated": datetime.now().isoformat(),
            "time_range_hours": hours_back,
            "total_records_found": len(records)
        }