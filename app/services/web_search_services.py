import json
import re
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple

import aiohttp
import openai
import requests
from anthropic import Anthropic
from bs4 import BeautifulSoup
from langchain.schema import HumanMessage, SystemMessage
from langchain.schema.output_parser import StrOutputParser
from langchain.schema.runnable import RunnablePassthrough
from langchain_community.chat_models import ChatPerplexity

from app.core.config import settings
from app.utils.llm_utils import general_call
from app.utils.logger import generic_logger as logger
from app.utils.comprehensive_cost_tracker import cost_tracker, track_web_search

# from app.utils.tool_utils import search_handler

# Initialize Anthropic client
client = Anthropic(api_key=settings.ANTHROPIC_API_KEY)

# Initialize OpenAI client
# openai_client = openai.OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
openai_client = openai.OpenAI(api_key=settings.OPENAI_API_KEY)

perplexity_client = openai.OpenAI(
    api_key=settings.PERPLEXITY_KEY, base_url="https://api.perplexity.ai"
)


async def search_google(query: str, num_results: int = 5) -> List[Dict[str, str]]:
    """
    Asynchronously search Google and return top results
    """
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36"
    }

    async with aiohttp.ClientSession() as session:
        url = f"https://www.google.com/search?q={query.replace(' ', '+')}&gl=us"

        try:
            async with session.get(url, headers=headers) as response:
                if response.status == 200:
                    html = await response.text()
                    soup = BeautifulSoup(html, "html.parser")
                    results = []

                    for result in soup.select(".tF2Cxc")[:num_results]:
                        title = result.select_one("h3").text
                        link = result.select_one("a")["href"]
                        results.append({"title": title, "link": link})

                    return results
                else:
                    print("Failed to fetch search results.")
                    return []
        except Exception as e:
            print(f"Search error: {e}")
            return []


async def web_scrape_search_bkp(search_query, flag=True):  # history: list = [],
    response = openai_client.responses.create(
        model=settings.GPT_4O_MINI_MODEL_NAME,
        tools=[
            {
                "type": "web_search_preview",
                "user_location": {"type": "approximate", "country": "IN"},
                "search_context_size": "medium",
            }
        ],
        temperature=0.4,
        max_output_tokens=3048,
        input=[
            {
                "role": "system",
                "content": f"""You are Vuetra's Financial/Stock and News AI Expert, a highly knowledgeable assistant specializing in financial analysis, market trends, technical data and news analysis. Your role is to provide users with accurate, detailed, and well-structured financial and news insights. Always provide the latest market information from websearch.

          ###While responding and analysing to user questions during analysis, the following key points should be kept in mind:
           - Maintain relevance to the question context.
           - Provide clear, concise, and actionable insights.
           - Ensure data-backed explanations where applicable.
           - Prioritize accuracy and user value in each response.
           - Where needed provide a explanation with proper data in table format.
           - If an external date is not explicitly mentioned in the user's question, the analysis should default to using the current date.
           - For analysis, always use reuters.com, marketwatch.com, markets.businessinsider.com, finance.yahoo.com and www.cnbc.com as a primary source of information.

            - Current date is -- {datetime.today().strftime('%d %B %Y')}

            ### Response Format must follow these rules:
            - *Provide responses only in inner HTML format, no markdown or plain text.*
            - All links and sources should be included using **inner HTML <a> anchor tags**. Double check the response, provided response should be syntactically correct and should be rendered using innerHTML.
            - Do not include any unnecessary characters in the response that doesn't seem good in UI like newlines, tabs, etc when we render with innerHTML.
            - Ensure clarity, conciseness, and factual accuracy.
            - Use bullet points, tables, and structured formatting where applicable.
            - No need to add this ([reuters.com](https://www.reuters.com/markets/currencies/dollar-stuck-near-5-month-low-struggles-shake-off-growth-concerns-2025-03-18/?utm_source=openai)) in answer.

            ### Response format must be like this:
            {{ "answer": "<p>Provide model's answer in <strong>HTML rich text with paragraphs and bullet points if needed</strong>.</p>",
                "sources": "[<a href='https://www.reuters.com/markets/europe/europes-fiscal-splurge-coul/' target='_blank'>europes-fiscal-splurge-coul</a>"]}}

            Example response format:
            {{ "answer": "<div>
                <p><strong>Updated and resolved both of his doubts regarding chart analysis and obtaining a scanned one.</strong></p>
                <h2>Here are the latest updates from Reuters and MarketWatch:</h2>
                <h3>Reuters:</h3>
                <ul>
                    <li><strong>Europe's Fiscal Measures Could Lead to Decade-Long Bull Market</strong></li>
                    <li>Analyst Klement suggests that substantial fiscal spending in Germany and the EU may initiate a decade-long bull market for European equities.</li>
                    <li>If these projections hold, Europe could witness a transformative shift in its global market leadership.</li>
                </ul>
                </div>",
                "sources": ["<a href='https://www.reuters.com/markets/europe/europes-fiscal-splurge-coul/' target='_blank'>europes-fiscal-splurge-coul</a>",
                            "<a href='https://www.reuters.com/markets/europe/europes-fiscal-splurge-coul/' target='_blank'>europes-fiscal-splurge-coul</a>"]}}
            """,
            },
            {"role": "user", "content": f"User question is: {search_query}"},
        ],
    )

    # Ensure response.output_text is used instead of response itself
    cleaned_output_text = re.sub(r"\?utm_source=openai", "", response.output_text)
    cleaned_output_text = re.sub(r"\\", "", cleaned_output_text)
    # cleaned_output_text = json.loads(cleaned_output_text)
    response = general_call(
        f"""
        analyse the search results and provide answer only in html, this should be directly rendered in UI with innerHTML, and if there are links available separate them and provide sources key.
        EARCH_RESULTS: {cleaned_output_text}
        *repsonse format*:
        {{
            "answer": "provide answer from the search results in html rich text with tags like <strong>, <p>, <ul>, <li> etc.",
            "sources": "[<a href='https://www.reuters.com/markets/europe/europes-fiscal-splurge-coul/' target='_blank'>europes-fiscal-splurge-coul</a>]"}}"""
    )
    try:
        response = json.loads(response)
        return {"response": response.get("answer"), "source": response.get("sources")}
    except Exception as e:
        return {"response": response, "source": []}


async def web_scrape_search_stream_bkp(search_query, flag=True):  # history: list = [],
    response = openai_client.responses.create(
        model=settings.GPT_4O_MINI_MODEL_NAME,
        tools=[
            {
                "type": "web_search_preview",
                "user_location": {"type": "approximate", "country": "US"},
                "search_context_size": "medium",
            }
        ],
        temperature=0.4,
        max_output_tokens=3048,
        input=[
            {
                "role": "system",
                "content": f"""You are Vuetra's Financial/Stock AI Expert, a highly knowledgeable assistant specializing in financial analysis, market trends, and technical data. Your role is to provide users with accurate, detailed, and well-structured financial insights.

          ###While responding and analysing to user questions during analysis, the following key points should be kept in mind:
           - Maintain relevance to the question context.
           - Provide clear, concise, and actionable insights.
           - Ensure data-backed explanations where applicable.
           - Prioritize accuracy and user value in each response.
           - Where needed provide a explanation with proper data in table format.
           - If an external date is not explicitly mentioned in the user's question, the analysis should default to using the current date.

            - Today's date is -- {datetime.today().strftime('%d %B %Y')}

            ### Response Format must follow these rules:
            - *Provide responses only in inner HTML format, no markdown or plain text.*
            - All links and sources should be included using **inner HTML <a> anchor tags**. Double check the response, provided response should be syntactically correct and should be rendered using innerHTML.
            - Do not include any unnecessary characters in the response that doesn't seem good in UI like newlines, tabs, etc when we render with innerHTML.
            - Ensure clarity, conciseness, and factual accuracy.
            - Use bullet points, tables, and structured formatting where applicable.
            - No need to add this ([reuters.com](https://www.reuters.com/markets/currencies/dollar-stuck-near-5-month-low-struggles-shake-off-growth-concerns-2025-03-18/?utm_source=openai)) in answer.

            ### Response format must be like this:
            {{ "answer": "<p>Provide model's answer in <strong>HTML rich text with paragraphs and bullet points if needed</strong>.</p>",
                "sources": "[<a href='https://www.reuters.com/markets/europe/europes-fiscal-splurge-coul/' target='_blank'>europes-fiscal-splurge-coul</a>"]}}

            Example response format:
            {{ "answer": "<div>
                <p><strong>Updated and resolved both of his doubts regarding chart analysis and obtaining a scanned one.</strong></p>
                <h2>Here are the latest updates from Reuters and MarketWatch:</h2>
                <h3>Reuters:</h3>
                <ul>
                    <li><strong>Europe's Fiscal Measures Could Lead to Decade-Long Bull Market</strong></li>
                    <li>Analyst Klement suggests that substantial fiscal spending in Germany and the EU may initiate a decade-long bull market for European equities.</li>
                    <li>If these projections hold, Europe could witness a transformative shift in its global market leadership.</li>
                </ul>
                </div>",
                "sources": ["<a href='https://www.reuters.com/markets/europe/europes-fiscal-splurge-coul/' target='_blank'>europes-fiscal-splurge-coul</a>",
                            "<a href='https://www.reuters.com/markets/europe/europes-fiscal-splurge-coul/' target='_blank'>europes-fiscal-splurge-coul</a>"]}}
            """,
            },
            {"role": "user", "content": f"User question is: {search_query}"},
        ],
    )

    cleaned_output_text = re.sub(r"\?utm_source=openai", "", response.output_text)
    cleaned_output_text = re.sub(r"\\", "", cleaned_output_text)
    prompt = (
        """
        analyse the search results and provide answer only in pure html, this should be directly rendered in UI with innerHTML, and if there are links available separate them and provide sources key.
        SEARCH_RESULTS:"""
        + cleaned_output_text
        + """
        *strict repsonse format*:
        </p/h1/ul/li>response</p/h1/ul/li>(in html should be directly rendered using innerHTML) ```{"sources": ["<a href='https://www.reuters.com/markets/europe/europes-fiscal-splurge-coul/' target='_blank'>europes-fiscal-splurge-coul</a>"]}```

        **Note**:
         - Do not include any language identifier like html after the backticks. Just use plain triple backticks before the JSON from SEARCH_RESULTS.
         - Never include ````html``` in the html response response.
         - Always include ```json``` in the response when json data is available.

        """
    )

    response = openai_client.chat.completions.create(
        model=settings.GPT_4O_MINI_MODEL_NAME,
        messages=[
            {"role": "system", "content": prompt},
        ],
        temperature=0.2,
        max_tokens=4096,
        top_p=0.2,
        stream=True,
    )
    chunks = []
    for chunk in response:
        if chunk.choices and chunk.choices[0].delta.content:
            chunks.append(chunk.choices[0].delta.content)
            yield chunk.choices[0].delta.content
    print(chunks, "===========================>chunks")


system_prompt = (
    f"""You are Vuetra's Financial/Stock AI Expert, a highly knowledgeable assistant specializing in financial analysis, market trends, and technical data. Your role is to provide users with accurate, detailed, and well-structured financial insights.
    ###While responding and analysing to user questions during analysis, the following key points should be kept in mind:
    - Maintain relevance to the question context.
    - Provide clear, concise, and actionable insights.
    - Ensure data-backed explanations where applicable.
    - Prioritize accuracy and user value in each response.
    - Where needed provide a explanation with proper data in table format.
    - If an external date is not explicitly mentioned in the user's question, the analysis should default to using the current date.

    - Today's date is --"""
    + datetime.today().strftime("%d %B %Y")
    + """

    ### Response Format must follow these rules:
    - *Provide responses only in inner HTML format, no markdown or plain text.*
    - All links and sources should be included using **inner HTML <a> anchor tags**. Double check the response, provided response should be syntactically correct and should be rendered using innerHTML.
    - Do not include any unnecessary characters in the response that doesn't seem good in UI like newlines, tabs, etc when we render with innerHTML.
    - Ensure clarity, conciseness, and factual accuracy.
    - Use bullet points, tables, and structured formatting where applicable.
    - No need to add this ([reuters.com](https://www.reuters.com/markets/currencies/dollar-stuck-near-5-month-low-struggles-shake-off-growth-concerns-2025-03-18/?utm_source=openai)) in answer.

    ### Response format must be like this:
    { "answer": "<p>Provide model's answer in <strong>HTML rich text with paragraphs and bullet points if needed</strong>.</p>",
        "sources": "[<a href='https://www.reuters.com/markets/europe/europes-fiscal-splurge-coul/' target='_blank'>europes-fiscal-splurge-coul</a>"]}

    Example response format:
    { "answer": "<div
        <p><strong>Updated and resolved both of his doubts regarding chart analysis and obtaining a scanned one.</strong></p>
        <h2>Here are the latest updates from Reuters and MarketWatch:</h2>
        <h3>Reuters:</h3>
        <ul>
            <li><strong>Europe's Fiscal Measures Could Lead to Decade-Long Bull Market</strong></li>
            <li>Analyst Klement suggests that substantial fiscal spending in Germany and the EU may initiate a decade-long bull market for European equities.</li>
            <li>If these projections hold, Europe could witness a transformative shift in its global market leadership.</li>
        </ul>
        </div>",
        "sources": ["<a href='https://www.reuters.com/markets/europe/europes-fiscal-splurge-coul/' target='_blank'>europes-fiscal-splurge-coul</a>",
                    "<a href='https://www.reuters.com/markets/europe/europes-fiscal-splurge-coul/' target='_blank'>europes-fiscal-splurge-coul</a>"]}
    **note**:
     - Always respond in json resonse, with keys "answer" and "sources"
     - response should be be in a json format where answer key should be in pure and valid html format, sources should be in list format
    """
)
system_prompt_stream = (
    f"""You are Vuetra's Financial/Stock AI Expert, a highly knowledgeable assistant specializing in financial analysis, market trends, and technical data. Your role is to provide users with accurate, detailed, and well-structured financial insights.
    ###While responding and analysing to user questions during analysis, the following key points should be kept in mind:
    - Maintain relevance to the question context.
    - Provide clear, concise, and actionable insights.
    - Ensure data-backed explanations where applicable.
    - Prioritize accuracy and user value in each response.
    - Where needed provide a explanation with proper data in table format.
    - If an external date is not explicitly mentioned in the user's question, the analysis should default to using the current date.

    - Today's date is --"""
    + datetime.today().strftime("%d %B %Y")
    + """


    *expected repsonse format*:
    Answer in pure and valid html format (which should be directly renderedy by innerHTML) followed by json format.
    ex:
       </p/h1/ul/li>response</p/h1/ul/li>(in html should be directly rendered using innerHTML) ```{"sources": ["<a href='https://www.reuters.com/markets/europe/europes-fiscal-splurge-coul/' target='_blank'>europes-fiscal-splurge-coul</a>"]}```

    **Note**:
        - Respond always First html response then json response.
        - Give this html response part only in pure, valid HTML. Do not use Markdown syntax like ###, **, -, or triple quotes. Wrap all content using appropriate HTML tags (<h1>, <p>, <ul>, <table>, etc.). Ensure the response can be directly inserted into the DOM using innerHTML.
        - Always include ```json``` in the response when json data is available.
    """
)


async def web_scrape_search(
    search_query: str,
    search_context_size: str = "low",
    user_location: Optional[Dict[str, Any]] = None,
) -> Tuple[str, Dict[str, Any]]:
    """
    Performs a web search using Perplexity API with the Sonar model and returns the response.

    Args:
        search_query: The user's question or search query
        search_context_size: How much search context to retrieve (low, medium, high)
        user_location: Optional location information to refine search results

    Returns:
        Tuple containing (response_text, usage_info)
    """

    api_key = settings.PERPLEXITY_KEY
    if not api_key:
        error_msg = "Error: PERPLEXITY_API_KEY environment variable is not set"
        logger.info(error_msg)
        return {}

    # Set up the API endpoint
    url = "https://api.perplexity.ai/chat/completions"

    # Validate search_context_size
    if search_context_size not in ["low", "medium", "high"]:
        search_context_size = "low"  # Default to low if invalid

    # Prepare web search options
    web_search_options = {"search_context_size": search_context_size}

    # Add user location if provided
    if user_location:
        web_search_options["user_location"] = user_location

    # Prepare the payload
    payload = {
        "model": settings.PERPLEXITY_MODEL_NAME,
        "messages": [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": search_query},
        ],
        "web_search_options": web_search_options,
    }

    # Set up headers
    headers = {"Authorization": f"Bearer {api_key}", "Content-Type": "application/json"}

    try:
        # Make the API request
        response = requests.post(url, json=payload, headers=headers)
        # Check if the request was successful
        if response.status_code == 200:
            result = response.json()
            # Extract the content from the response
            if "choices" in result and len(result["choices"]) > 0:
                content = result["choices"][0]["message"]["content"]
                content = content.strip("```json").strip("```html").strip("`")
                try:
                    content = json.loads(content)
                except json.JSONDecodeError:
                    pass
                return content
            else:
                error_msg = (
                    f"Error: Unexpected response format from Perplexity API: {result}"
                )
                logger.info(error_msg)
                return {}
        else:
            error_msg = f"Error: Perplexity API returned status code {response.status_code}: {response.text}"
            logger.info(error_msg)
            return {}
    except Exception as e:
        error_msg = f"Error executing Perplexity search: {str(e)}"
        logger.info(error_msg)
        return {}


async def web_scrape_search_v3(
    search_query: str,
    search_context_size: str = "low",
    user_location: Optional[Dict[str, Any]] = None,
) -> Tuple[str, Dict[str, Any]]:
    """
    Performs a web search using Perplexity API with the Sonar model and returns the response.

    Args:
        search_query: The user's question or search query
        search_context_size: How much search context to retrieve (low, medium, high)
        user_location: Optional location information to refine search results

    Returns:
        Tuple containing (response_text, usage_info)
    """

    api_key = settings.PERPLEXITY_KEY
    if not api_key:
        error_msg = "Error: PERPLEXITY_API_KEY environment variable is not set"
        logger.info(error_msg)
        return {}

    # Validate search_context_size
    if search_context_size not in ["low", "medium", "high"]:
        search_context_size = "low"  # Default to low if invalid

    # Prepare web search options
    web_search_options = {"search_context_size": search_context_size}

    # Add user location if provided
    if user_location:
        web_search_options["user_location"] = user_location

    try:
        import time
        start_time = time.time()

        # Create LangChain components
        llm = ChatPerplexity(
            model=settings.PERPLEXITY_MODEL_NAME,
            pplx_api_key=api_key,
            web_search_options=web_search_options,
        )

        # Build messages
        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=search_query),
        ]

        # Create chain
        chain = RunnablePassthrough() | llm | StrOutputParser()

        # Execute chain
        response = await chain.ainvoke(messages)

        # Track cost
        duration_ms = (time.time() - start_time) * 1000
        input_text = f"{system_prompt}\n{search_query}"
        cost_tracker.track_model_usage(
            model_name=settings.PERPLEXITY_MODEL_NAME,
            function_name="web_scrape_search_v3",
            input_text=input_text,
            output_text=response,
            duration_ms=duration_ms
        )

        # Track web search call
        cost_tracker.track_web_search_call(search_context_size)

        # Process response
        content = response.strip("```json").strip("```html").strip("`")
        try:
            content = json.loads(content)
        except json.JSONDecodeError:
            pass

        return content

    except Exception as e:
        error_msg = f"Error executing Perplexity search: {str(e)}"
        logger.info(error_msg)
        return {}


async def web_scrape_search_stream(
    search_query: str,
    search_context_size: str = "low",
    user_location: Optional[Dict[str, Any]] = None,
):
    """
    Performs a streaming web search using Perplexity API with the Sonar model.

    Args:
        search_query: The user's question or search query
        search_context_size: How much search context to retrieve (low, medium, high)
        user_location: Optional location information to refine search results

    Yields:
        Chunks of the response as they become available
    """
    # Validate search_context_size
    if search_context_size not in ["low", "medium", "high"]:
        search_context_size = "low"  # Default to low if invalid

    # Prepare web search options
    web_search_options = {"search_context_size": search_context_size}

    # Add user location if provided
    if user_location:
        web_search_options["user_location"] = user_location

    try:
        # Use the existing perplexity_client with OpenAI wrapper
        response_stream = perplexity_client.chat.completions.create(
            model=settings.PERPLEXITY_MODEL_NAME,
            messages=[
                {"role": "system", "content": system_prompt_stream},
                {"role": "user", "content": search_query},
            ],
            web_search_options=web_search_options,
            stream=True,
        )
        for response in response_stream:
            if response.choices and len(response.choices) > 0:
                delta = response.choices[0].delta
                content = delta.content
                if content:
                    yield content

    except Exception as e:
        error_msg = f"Error executing Perplexity streaming search: {str(e)}"
        yield error_msg


async def web_scrape_search_stream_v3(
    search_query: str,
    search_context_size: str = "low",
    user_location: Optional[Dict[str, Any]] = None,
):
    """
    Performs a streaming web search using Perplexity API with the Sonar model.

    Args:
        search_query: The user's question or search query
        search_context_size: How much search context to retrieve (low, medium, high)
        user_location: Optional location information to refine search results

    Yields:
        Chunks of the response as they become available
    """
    # Validate search_context_size
    if search_context_size not in ["low", "medium", "high"]:
        search_context_size = "low"  # Default to low if invalid

    # Prepare web search options
    web_search_options = {"search_context_size": search_context_size}

    # Add user location if provided
    if user_location:
        web_search_options["user_location"] = user_location

    try:
        # Create LangChain components
        llm = ChatPerplexity(
            model=settings.PERPLEXITY_MODEL_NAME,
            pplx_api_key=settings.PERPLEXITY_KEY,
            web_search_options=web_search_options,
            streaming=True,
        )

        # Build messages
        messages = [
            SystemMessage(content=system_prompt_stream),
            HumanMessage(content=search_query),
        ]

        # Create chain
        chain = RunnablePassthrough() | llm | StrOutputParser()

        # Stream the response
        async for chunk in chain.astream(messages):
            if chunk:
                yield chunk

    except Exception as e:
        error_msg = f"Error executing Perplexity streaming search: {str(e)}"
        yield error_msg
