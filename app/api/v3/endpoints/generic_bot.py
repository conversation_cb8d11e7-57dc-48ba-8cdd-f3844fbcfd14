import uuid
import time

from fastapi import API<PERSON>out<PERSON>, Depends, HTTPException, Request
from fastapi.background import BackgroundTasks
from fastapi.responses import StreamingResponse, JSONResponse
from fastapi_pagination import Params, add_pagination
from openai import OpenAI
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.crud.chat_crud import create_chat, create_conversation
from app.db.database import get_db
from app.schemas.chat_schemas import GenericChatSchema, ChartDataBasedLensSchema
from app.services.deep_search_service import deep_search_stream_handler_v3
from app.services.generic_chat_service import streaming_generic_chat_service_v3, get_chart_data_based_on_lens_id
from app.utils import cache
from app.utils.general_utils import generate_chat_name
from app.utils.logger import generic_logger as logger
from app.utils.comprehensive_cost_tracker import cost_tracker

client = OpenAI(api_key=settings.OPENAI_API_KEY)

router = APIRouter()
add_pagination(router)


async def create_root_chat(
    db: AsyncSession, account_id: str, question="", chat_type="NORMAL"
):
    chat_id = str(uuid.uuid4())
    parent = chat_id
    await create_conversation(
        db,
        chat_id=chat_id,
        account_id=account_id,
        chat_type=chat_type,
        name=generate_chat_name(question),
    )
    await create_chat(chat_data={"id": chat_id, "chat_id": chat_id, "parent": parent})
    return chat_id, parent


@router.post("/generic-v3")
# @ai_call_limit
async def generic(
    request: Request,
    data: GenericChatSchema,
    params: Params = Depends(),
    db: AsyncSession = Depends(get_db),
    background_tasks: BackgroundTasks = BackgroundTasks(),
):
    """
    Handles generic chat requests with a question, reply (optional), and account details.
    Validates inputs, handles chat branching logic, and constructs a user question.
    Retrieves chat history and checks if the request is an image-based request.
    If it is, calls the image chat service. Otherwise, calls the generic chat service.
    Returns the response from the service.

    Supports both single selection and multiple selections for comparative analysis.
    """
    # Start cost tracking
    request_start_time = time.time()
    customer_id = request.state.customer_id
    chat_id = data.chat_id
    track = f"{customer_id}_{data.account_id or 0}"

    # Initialize cost tracking
    request_id = cost_tracker.start_request_tracking(
        endpoint="/generic-v3",
        question=data.question,
        customer_id=customer_id,
        account_id=data.account_id or "",
        chat_id=chat_id or ""
    )
    context_chat_key = f"context_chat_key_{track}"
    context_chat = cache.get(context_chat_key) or {}
    if context_chat:
        data.chat_id = context_chat.get("chat_id")
        chat_id = data.chat_id
        data.parent = context_chat.get("parent")
        cache.delete(context_chat_key)
    logger.debug(f"Initializing chat service for customer: {customer_id}")
    if not data.chat_id:
        chat_id, parent = await create_root_chat(
            db, account_id=track, question=data.question
        )
        data.chat_id = chat_id
        data.parent = parent

    # Validate inputs early
    if not data.question:
        raise HTTPException(status_code=400, detail="No question provided")
    if not chat_id:
        raise HTTPException(status_code=400, detail="No chat_id provided")

    # Construct user question
    user_question = (
        f"Reply to: {data.reply}\n\nQuestion: {data.question}"
        if data.reply
        else data.question
    )

    # Add symbol information if provided
    if data.symbol:
        user_question += f" for Symbol: {data.symbol}"

    # Handle multiple selections for comparative analysis
    if data.selections and len(data.selections) > 0:
        user_question += " for comparative analysis of the following items:"

        for i, selection in enumerate(data.selections):
            if selection.category.lower() == "indicators":
                user_question += f"\n{i+1}. Indicator: {selection.option}"
            elif selection.category.lower() == "data":
                user_question += f"\n{i+1}. Category: {selection.option}, Data: {selection.sub_option}"
            else:  # Symbols
                user_question += f"\n{i+1}. Category: {selection.option}, Symbol: {selection.sub_option}"

        # Make it very clear this is a comparative analysis request
        user_question += "\n\nPlease provide a detailed comparison of these items."

        logger.info(f"Constructed comparative analysis question: {user_question}")

    # Handle single selection (backward compatibility)
    elif data.selected_category:
        if data.selected_category.lower() == "indicators":
            user_question += f" for Indicator: {data.selected_option}"
        elif data.selected_category.lower() == "data":
            user_question += f" for Category: {data.selected_option} For data: {data.selected_sub_option}"
        else:
            user_question += f" for Catogory: {data.selected_option} For symbol: {data.selected_sub_option}"

    logger.info(
        f"User Question: {user_question} | Websearch: {data.is_websearch_enabled}"
    )

    # Retrieve chat history
    history_key = f"chat_history_{chat_id}"
    generic_chat_history = cache.get(history_key) or []
    cache.set(history_key, generic_chat_history)

    # Process with generic chat service
    logger.info(f"Going into generic chat service: {chat_id}")

    # Check if deep search is requested
    if data.is_deep_search:
        logger.info(f"Deep search requested for question: {user_question}")
        cost_tracker.set_flow_type("deep_search")

        async def deep_search_with_cost_tracking():
            try:
                async for chunk in deep_search_stream_handler_v3(
                    db,
                    chat_id,
                    data.account_id,
                    user_question,
                    generic_chat_history,
                    reply=data.reply,
                    track=track,
                    parent=data.parent,
                ):
                    yield chunk
            finally:
                # Finalize cost tracking
                request_duration_ms = (time.time() - request_start_time) * 1000
                cost_tracker.finalize_request_tracking(request_duration_ms)

        return StreamingResponse(
            deep_search_with_cost_tracking(),
            media_type="text/event-stream",
            headers={
                "Content-Type": "text/event-stream",
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
            },
        )


    data.selections = [selection.model_dump() for selection in data.selections]

    # Set flow type for generic chat
    cost_tracker.set_flow_type("generic_chat")

    # Convert chartData to dict format for MongoDB storage
    async def generic_chat_with_cost_tracking():
        try:
            async for chunk in streaming_generic_chat_service_v3(
                db,
                chat_id,
                data.account_id,
                data.trading_token,
                params,
                data.lens_id or "",
                data,
                generic_chat_history=generic_chat_history,
                is_websearch_enabled=data.is_websearch_enabled,
                question=user_question,
                reply=data.reply,
                token=data.token,
                track=track,
                selected_category=data.selected_category,
                selected_option=data.selected_option,
                background_tasks=background_tasks,
                parent=data.parent,
                edited_question=data.edited_question,
                original_question=data.question,
                selections=data.selections,
                file_paths=data.file_paths,
            ):
                yield chunk
        finally:
            # Finalize cost tracking
            request_duration_ms = (time.time() - request_start_time) * 1000
            cost_tracker.finalize_request_tracking(request_duration_ms)

    response = StreamingResponse(
        generic_chat_with_cost_tracking(),
        media_type="text/event-stream",
        headers={
            "Content-Type": "text/event-stream",
            "Cache-Control": "no-store",
            "Connection": "keep-alive",
            "Transfer-Encoding": "chunked",
        },
    )
    return response  # Final response if no image-based response was found

@router.get("/get-chart-data")
async def get_chart_data(
    request: Request,
    lens_id: str = "",
    account_id: str = "",
    params: Params = Depends(),
    db: AsyncSession = Depends(get_db),
):
    customer_id = request.state.customer_id
    track = f"{customer_id}_{account_id or 0}"
    chart_data = await get_chart_data_based_on_lens_id(
        db = db,
        lens_id=lens_id,
        track=track
    )
    return JSONResponse(chart_data)
