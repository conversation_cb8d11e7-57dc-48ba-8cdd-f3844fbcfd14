from fastapi import API<PERSON><PERSON><PERSON>, Query, HTTPException
from fastapi.responses import HTMLResponse
from typing import Optional
import json

from app.services.cost_dashboard_service import CostDashboardService

router = APIRouter()

# Initialize dashboard service
dashboard_service = CostDashboardService()

@router.get("/dashboard")
async def get_cost_dashboard():
    """Serve the cost analysis dashboard HTML page"""
    html_content = """
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>AI Cost Analysis Dashboard</title>
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }

            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                padding: 20px;
            }

            .dashboard-container {
                max-width: 1400px;
                margin: 0 auto;
                background: white;
                border-radius: 15px;
                box-shadow: 0 20px 40px rgba(0,0,0,0.1);
                overflow: hidden;
            }

            .header {
                background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
                color: white;
                padding: 30px;
                text-align: center;
            }

            .header h1 {
                font-size: 2.5em;
                margin-bottom: 10px;
                font-weight: 300;
            }

            .header p {
                font-size: 1.1em;
                opacity: 0.9;
            }

            .stats-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 20px;
                padding: 30px;
                background: #f8f9fa;
            }

            .stat-card {
                background: white;
                padding: 25px;
                border-radius: 10px;
                box-shadow: 0 5px 15px rgba(0,0,0,0.08);
                text-align: center;
                transition: transform 0.3s ease;
            }

            .stat-card:hover {
                transform: translateY(-5px);
            }

            .stat-value {
                font-size: 2.5em;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 10px;
            }

            .stat-label {
                color: #7f8c8d;
                font-size: 1.1em;
                text-transform: uppercase;
                letter-spacing: 1px;
            }

            .charts-section {
                padding: 30px;
            }

            .charts-grid {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 30px;
                margin-bottom: 30px;
            }

            .chart-container {
                background: white;
                padding: 25px;
                border-radius: 10px;
                box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            }

            .chart-title {
                font-size: 1.3em;
                color: #2c3e50;
                margin-bottom: 20px;
                text-align: center;
                font-weight: 600;
            }

            .table-container {
                background: white;
                border-radius: 10px;
                box-shadow: 0 5px 15px rgba(0,0,0,0.08);
                overflow: hidden;
                margin-top: 30px;
            }

            .table-title {
                background: #34495e;
                color: white;
                padding: 20px;
                font-size: 1.3em;
                font-weight: 600;
            }

            table {
                width: 100%;
                border-collapse: collapse;
            }

            th, td {
                padding: 15px;
                text-align: left;
                border-bottom: 1px solid #ecf0f1;
            }

            th {
                background: #f8f9fa;
                font-weight: 600;
                color: #2c3e50;
            }

            tr:hover {
                background: #f8f9fa;
            }

            .loading {
                text-align: center;
                padding: 50px;
                color: #7f8c8d;
                font-size: 1.2em;
            }

            .refresh-btn {
                background: #3498db;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 5px;
                cursor: pointer;
                font-size: 1em;
                margin: 20px;
                transition: background 0.3s ease;
            }

            .refresh-btn:hover {
                background: #2980b9;
            }

            .cost-badge {
                background: #e74c3c;
                color: white;
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 0.9em;
            }

            .flow-badge {
                background: #9b59b6;
                color: white;
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 0.9em;
            }

            @media (max-width: 768px) {
                .charts-grid {
                    grid-template-columns: 1fr;
                }

                .stats-grid {
                    grid-template-columns: 1fr;
                }
            }
        </style>
    </head>
    <body>
        <div class="dashboard-container">
            <div class="header">
                <h1>🤖 AI Cost Analysis Dashboard</h1>
                <p>Real-time monitoring of AI model usage and costs</p>
                <button class="refresh-btn" onclick="loadDashboardData()">🔄 Refresh Data</button>
            </div>

            <div class="stats-grid" id="statsGrid">
                <div class="loading">Loading dashboard data...</div>
            </div>

            <div class="charts-section">
                <div class="charts-grid">
                    <div class="chart-container">
                        <div class="chart-title">💰 Cost by Model</div>
                        <canvas id="modelCostChart"></canvas>
                    </div>
                    <div class="chart-container">
                        <div class="chart-title">🔄 Requests by Flow Type</div>
                        <canvas id="flowChart"></canvas>
                    </div>
                </div>

                <div class="chart-container">
                    <div class="chart-title">📈 Hourly Cost Trend</div>
                    <canvas id="hourlyChart"></canvas>
                </div>

                <div class="table-container">
                    <div class="table-title">📋 Recent Requests</div>
                    <div style="overflow-x: auto;">
                        <table id="recentRequestsTable">
                            <thead>
                                <tr>
                                    <th>Request ID</th>
                                    <th>Flow Type</th>
                                    <th>Question</th>
                                    <th>Models Used</th>
                                    <th>Cost</th>
                                    <th>Tokens</th>
                                    <th>Duration</th>
                                    <th>Time</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr><td colspan="8" class="loading">Loading recent requests...</td></tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <script>
            let charts = {};

            async function loadDashboardData() {
                try {
                    const response = await fetch('/api/v1/cost-dashboard/data');
                    const data = await response.json();

                    updateStats(data.stats);
                    updateCharts(data.stats);
                    updateRecentRequests(data.stats.recent_requests);

                } catch (error) {
                    console.error('Error loading dashboard data:', error);
                    document.getElementById('statsGrid').innerHTML =
                        '<div class="loading">❌ Error loading data. Please check if the API is running.</div>';
                }
            }

            function updateStats(stats) {
                const statsGrid = document.getElementById('statsGrid');
                statsGrid.innerHTML = `
                    <div class="stat-card">
                        <div class="stat-value">$${stats.total_cost.toFixed(4)}</div>
                        <div class="stat-label">Total Cost</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">${stats.total_requests.toLocaleString()}</div>
                        <div class="stat-label">Total Requests</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">${stats.total_tokens.toLocaleString()}</div>
                        <div class="stat-label">Total Tokens</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">$${stats.avg_cost_per_request.toFixed(4)}</div>
                        <div class="stat-label">Avg Cost/Request</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">${Math.round(stats.avg_tokens_per_request).toLocaleString()}</div>
                        <div class="stat-label">Avg Tokens/Request</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">${Math.round(stats.avg_duration_ms)}ms</div>
                        <div class="stat-label">Avg Duration</div>
                    </div>
                `;
            }

            function updateCharts(stats) {
                updateModelCostChart(stats.top_models);
                updateFlowChart(stats.top_flows);
                updateHourlyChart(stats.cost_by_hour);
            }

            function updateModelCostChart(topModels) {
                const ctx = document.getElementById('modelCostChart').getContext('2d');

                if (charts.modelCostChart) {
                    charts.modelCostChart.destroy();
                }

                const colors = ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', '#FF9F40'];

                charts.modelCostChart = new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        labels: topModels.map(m => m.model),
                        datasets: [{
                            data: topModels.map(m => m.cost),
                            backgroundColor: colors.slice(0, topModels.length),
                            borderWidth: 2,
                            borderColor: '#fff'
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: { position: 'bottom' },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        const model = topModels[context.dataIndex];
                                        return `${context.label}: $${model.cost.toFixed(4)} (${model.calls} calls)`;
                                    }
                                }
                            }
                        }
                    }
                });
            }

            function updateFlowChart(topFlows) {
                const ctx = document.getElementById('flowChart').getContext('2d');

                if (charts.flowChart) {
                    charts.flowChart.destroy();
                }

                charts.flowChart = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: topFlows.map(f => f.flow),
                        datasets: [{
                            label: 'Requests',
                            data: topFlows.map(f => f.requests),
                            backgroundColor: '#36A2EB'
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: { legend: { display: false } },
                        scales: { y: { beginAtZero: true } }
                    }
                });
            }

            function updateHourlyChart(costByHour) {
                const ctx = document.getElementById('hourlyChart').getContext('2d');

                if (charts.hourlyChart) {
                    charts.hourlyChart.destroy();
                }

                charts.hourlyChart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: costByHour.map(h => new Date(h.hour).toLocaleTimeString()),
                        datasets: [{
                            label: 'Cost ($)',
                            data: costByHour.map(h => h.cost),
                            borderColor: '#FF6384',
                            backgroundColor: 'rgba(255, 99, 132, 0.1)',
                            fill: true
                        }]
                    },
                    options: {
                        responsive: true,
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    callback: function(value) {
                                        return '$' + value.toFixed(4);
                                    }
                                }
                            }
                        }
                    }
                });
            }

            function updateRecentRequests(recentRequests) {
                const tbody = document.querySelector('#recentRequestsTable tbody');

                if (!recentRequests || recentRequests.length === 0) {
                    tbody.innerHTML = '<tr><td colspan="8" class="loading">No recent requests found</td></tr>';
                    return;
                }

                tbody.innerHTML = recentRequests.map(request => `
                    <tr>
                        <td title="${request.request_id}">${request.request_id.substring(0, 8)}...</td>
                        <td><span class="flow-badge">${request.flow_type}</span></td>
                        <td title="${request.question}">${request.question.substring(0, 50)}${request.question.length > 50 ? '...' : ''}</td>
                        <td>${request.models_used.join(', ')}</td>
                        <td><span class="cost-badge">$${request.total_cost.toFixed(4)}</span></td>
                        <td>${request.total_tokens.toLocaleString()}</td>
                        <td>${Math.round(request.duration_ms)}ms</td>
                        <td>${new Date(request.timestamp).toLocaleTimeString()}</td>
                    </tr>
                `).join('');
            }

            // Load data on page load
            document.addEventListener('DOMContentLoaded', loadDashboardData);

            // Auto-refresh every 30 seconds
            setInterval(loadDashboardData, 30000);
        </script>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content)

@router.get("/data")
async def get_dashboard_data(
    hours_back: int = Query(24, description="Hours of data to retrieve", ge=1, le=168)
):
    """Get dashboard data for the specified time range"""
    try:
        data = await dashboard_service.get_dashboard_data(hours_back)
        return data
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving dashboard data: {str(e)}")

@router.get("/request/{request_id}")
async def get_request_details(request_id: str):
    """Get detailed information about a specific request"""
    try:
        records = dashboard_service.parse_cost_analysis_from_logs(hours_back=168)  # Last week

        for record in records:
            if record.request_id == request_id:
                return {
                    "request": {
                        "request_id": record.request_id,
                        "endpoint": record.endpoint,
                        "flow_type": record.flow_type,
                        "customer_id": record.customer_id,
                        "account_id": record.account_id,
                        "chat_id": record.chat_id,
                        "question": record.question,
                        "request_duration_ms": record.request_duration_ms,
                        "total_cost": record.total_cost,
                        "total_input_tokens": record.total_input_tokens,
                        "total_output_tokens": record.total_output_tokens,
                        "total_tokens": record.total_tokens,
                        "timestamp": record.timestamp.isoformat(),
                        "model_usages": record.model_usages,
                        "web_search_calls": record.web_search_calls,
                        "web_search_cost": record.web_search_cost
                    }
                }

        raise HTTPException(status_code=404, detail="Request not found")

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving request details: {str(e)}")

@router.get("/models")
async def get_model_analytics(hours_back: int = Query(24, ge=1, le=168)):
    """Get detailed model usage analytics"""
    try:
        records = dashboard_service.parse_cost_analysis_from_logs(hours_back)

        model_details = {}
        for record in records:
            for usage in record.model_usages:
                model_name = usage["model_name"]
                if model_name not in model_details:
                    model_details[model_name] = {
                        "total_calls": 0,
                        "total_cost": 0.0,
                        "total_input_tokens": 0,
                        "total_output_tokens": 0,
                        "functions_used": set(),
                        "avg_cost_per_call": 0.0,
                        "requests_count": 0
                    }

                model_details[model_name]["total_calls"] += usage["api_calls"]
                model_details[model_name]["total_cost"] += usage["total_cost"]
                model_details[model_name]["total_input_tokens"] += usage["input_tokens"]
                model_details[model_name]["total_output_tokens"] += usage["output_tokens"]
                model_details[model_name]["functions_used"].update(usage["functions"])
                model_details[model_name]["requests_count"] += 1

        # Convert sets to lists and calculate averages
        for model_name, details in model_details.items():
            details["functions_used"] = list(details["functions_used"])
            if details["total_calls"] > 0:
                details["avg_cost_per_call"] = details["total_cost"] / details["total_calls"]

        return {
            "model_analytics": model_details,
            "time_range_hours": hours_back,
            "total_models": len(model_details)
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving model analytics: {str(e)}")