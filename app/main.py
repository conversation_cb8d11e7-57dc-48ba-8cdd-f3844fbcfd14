import os
from contextlib import asynccontextmanager

from fastapi import FastAP<PERSON>
from fastapi.middleware import Middleware
from fastapi.middleware.cors import CORSMiddleware
from fastapi.openapi.utils import get_openapi

from app.api.v1.api_v1 import api_v1
from app.api.v2.api_v2 import api_v2
from app.api.v3.api_v3 import api_v3
from app.core.config import settings
from app.crud.agents_crud import create_tool
from app.db.database import async_session
from app.schemas.enums import ToolEnum
from app.services.similarity_search.s3_similarity_engine import S3SimilarityEngine
from app.utils.logger import chatbot_logger as logger

from .middlewares import JWTAuthMiddleware

logger.info("Starting app")


@asynccontextmanager
async def lifespan(app: FastAPI):
    print("-" * 50, "starting app", "-" * 50)
    try:
        # Initialize S3-based similarity search engine
        print("Initializing S3-based similarity search engine...")

        # Look for FAISS index and S3 URLs file
        index_path = "my_index_hnsw.faiss"
        s3_urls_file = "my_index_hnsw.faiss.s3_urls"

        if os.path.exists(index_path) and os.path.exists(s3_urls_file):
            print(f"Loading S3 similarity search index: {index_path}")
            print(f"Using S3 URLs file: {s3_urls_file}")

            try:
                similarity_engine = S3SimilarityEngine(
                    index_path=index_path,
                    s3_urls_file=s3_urls_file,
                    chart_data_prefix="public/ai/lookalike/chart_data/",
                    csv_prefix="public/ai/lookalike/Look-a-like/",
                )
                app.state.similarity_engine = similarity_engine
                print("✅ S3-based similarity search engine initialized successfully")
            except Exception as e:
                print(f"❌ Failed to initialize S3 similarity search engine: {e}")
                app.state.similarity_engine = None
        else:
            print(
                "⚠️ FAISS index or S3 URLs file not found. Similarity search will be disabled."
            )
            print(
                f"  - Index file: {index_path} ({'✓' if os.path.exists(index_path) else '✗'})"
            )
            print(
                f"  - S3 URLs file: {s3_urls_file} ({'✓' if os.path.exists(s3_urls_file) else '✗'})"
            )
            app.state.similarity_engine = None

        # Initialize tools for staging/production
        if settings.ENVIRONMENT == "staging" or settings.ENVIRONMENT == "production":
            async with async_session() as db:
                for tool in ToolEnum:
                    print(f"Creating tool: {tool.name}")
                    await create_tool(db, tool_name=tool.name)
        yield
    finally:
        pass

swagger_enabled_envs = ["development", "staging", "production"]
logger.info("Creating FastAPI app")
app = FastAPI(
    description="VueBrain",
    title="VueBrain AI Assistant",
    lifespan=lifespan,
    openapi_url="/openapi.json",
    redoc_url="/read-docs" if settings.ENVIRONMENT in swagger_enabled_envs else None,
    docs_url="/test-apis" if settings.ENVIRONMENT in swagger_enabled_envs else None,
    middleware=[Middleware(JWTAuthMiddleware)],
    # debug=True if settings.ENVIRONMENT == "development" else False,
)


def custom_openapi():
    if app.openapi_schema:
        return app.openapi_schema
    openapi_schema = get_openapi(
        title=app.title,
        version="1.0.0",
        routes=app.routes,
    )
    openapi_schema["components"]["securitySchemes"] = {
        "BearerAuth": {"type": "http", "scheme": "bearer"}
    }
    for path in openapi_schema["paths"].values():
        for method in path.values():
            method["security"] = [{"BearerAuth": []}]
    app.openapi_schema = openapi_schema
    return app.openapi_schema


app.openapi = custom_openapi

allowed_origins = settings.ALLOWED_ORIGINS

app.add_middleware(
    CORSMiddleware,
    allow_origins=["https://workspace.vuetra.com"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
    allow_headers=["Authorization", "Content-Type"],
)

@app.get("/ping")
def ping():
    return "pong"

app.include_router(api_v1, prefix="/api/v1")
app.include_router(api_v2, prefix="/api/v2",)

app.include_router(
    api_v3,
    prefix="/api/v3",
)
