from datetime import datetime

from langchain.schema import Document
from langchain_core.messages import AIMessage, HumanMessage, SystemMessage
from langchain_groq import ChatGroq
from langchain_openai import ChatOpenAI
from openai import OpenAI

from app.core.config import settings
from app.models.models import Agent, Command
from app.prompts.agent_prompts import (
    decide_generic_or_not_prompt_gen,
    system_prompt_gen_v2,
)
from app.schemas.enums import LLMEnum, ToolEnum
from app.utils.logger import agent_logger as logger


def get_llm_for_agent(agent: Agent):
    llm = None
    llm = ChatOpenAI(
        api_key=settings.OPENAI_API_KEY,
        model=settings.GPT_4O_MINI_MODEL_NAME,
        temperature=agent.temperature,
        max_tokens=4096,
        max_retries=2,
        verbose=True,
    )
    return llm
    if agent.llm_type == LLMEnum.groq.value:
        llm = ChatGroq(
            api_key=settings.GROQ_API_KEY,
            model="mistral-saba-24b",
            temperature=agent.temperature,
            max_tokens=4096,
            max_retries=2,
            verbose=True,
        )
    elif agent.llm_type == LLMEnum.openai_4o_mini.value:
        llm = ChatOpenAI(
            api_key=settings.OPENAI_API_KEY,
            model=settings.GPT_4O_MINI_MODEL_NAME,
            temperature=agent.temperature,
            max_tokens=4096,
            max_retries=2,
            verbose=True,
        )
    elif agent.llm_type == LLMEnum.llama3_1_70b_versatile.value:
        llm = ChatGroq(
            api_key=settings.GROQ_API_KEY,
            model="llama-3.3-70b-versatile",
            temperature=agent.temperature,
            max_tokens=4096,
            max_retries=2,
            verbose=True,
        )
    elif agent.llm_type == LLMEnum.llama2_9b_it.value:
        llm = ChatGroq(
            api_key=settings.GROQ_API_KEY,
            model=LLMEnum.llama2_9b_it.value,
            temperature=agent.temperature,
            max_tokens=4096,
            max_retries=2,
            verbose=True,
        )
    else:
        llm = ChatOpenAI(
            api_key=settings.OPENAI_API_KEY,
            model=settings.MODEL_NAME,
            temperature=agent.temperature,
            max_tokens=4096,
            max_retries=2,
            verbose=True,
        )
    return llm


def get_openai_model():
    return ChatOpenAI(
        api_key=settings.OPENAI_API_KEY,
        model=settings.MODEL_NAME,
        temperature=0,
        max_tokens=4096,
        max_retries=2,
    )


def get_openai_model_v3():
    return ChatOpenAI(
        api_key=settings.OPENAI_API_KEY,
        model="gpt-4.1",
        temperature=0.2,
        max_tokens=4096,
        max_retries=2,
    )


def get_general_model():
    return ChatOpenAI(
        api_key=settings.OPENAI_API_KEY,
        temperature=0,
        max_tokens=4096,
        max_retries=2,
    )


def call_llm(
    chat_history: list[dict],
    question: str,
    contexts: list[Document],
    agent: Agent,
    tool_response: str | None = None,
    command: Command | None = None,
    tools=[],
) -> str:
    llm = get_llm_for_agent(agent)
    system_prompt = system_prompt_gen_v2(agent=agent, command=command, tools=tools)
    logger.info(f"System prompt: \n{system_prompt}")
    print(system_prompt, "-------------SYSTEM_PROMPT_______________________________")
    system_message = SystemMessage(content=system_prompt)
    formatted_chat_history = []
    for entry in chat_history:
        # history_question = (
        #     entry.get("question")
        #     if entry.get("question")
        #     else entry.get("Question") if entry.get("Question") else ""
        # )
        formatted_chat_history.append(HumanMessage(content=entry[0]))
        # history_answer = (
        #     entry.get("answer")
        #     if entry.get("answer")
        #     else entry.get("Answer") if entry.get("Answer") else ""
        # )
        formatted_chat_history.append(AIMessage(content=entry[1]))

    user_message = HumanMessage(content=question)

    context_content = ""
    if contexts is not None:
        context_content = "\n".join([doc.page_content for doc in contexts])
        context_content = f"***Context from RAG - consider this context to answer the question***: {context_content}"

    if tool_response is not None:
        context_content = f"***Context From Tool - consider this context to answer the question***: {tool_response}"
    context_message = SystemMessage(content=context_content)

    logger.info(f"Context message: {context_message}")
    messages = []
    print(system_message, "-" * 150)
    print(context_message, "-" * 150)
    print(user_message, "-" * 150)

    messages.append(system_message)
    messages.append(context_message)
    # messages.extend(formatted_chat_history)
    messages.append(user_message)
    print(
        "\n".join([str(msg) for msg in messages]),
        "--------------------------------messages--------------------------------",
    )
    response = llm.invoke(input=messages)
    return response


def stream_call_llm(
    chat_history: list[dict],
    question: str,
    contexts: list[Document],
    agent: Agent,
    tool_response: str | None = None,
    command: Command | None = None,
    tools=[],
) -> str:
    llm = get_llm_for_agent(agent)
    system_prompt = system_prompt_gen_v2(agent=agent, command=command, tools=tools)
    system_message = SystemMessage(content=system_prompt)

    formatted_chat_history = []
    for entry in chat_history:
        # history_question = (
        #     entry.get("question")
        #     if entry.get("question")
        #     else entry.get("Question") if entry.get("Question") else ""
        # )
        formatted_chat_history.append(HumanMessage(content=entry[0]))
        # history_answer = (
        #     entry.get("answer")
        #     if entry.get("answer")
        #     else entry.get("Answer") if entry.get("Answer") else ""
        # )
        formatted_chat_history.append(AIMessage(content=entry[1]))

    user_message = HumanMessage(content=question)

    context_content = ""
    if contexts is not None:
        context_content = "\n".join([doc.page_content for doc in contexts])
        context_content = f"***Context from RAG - consider this context to answer the question***: {context_content}"

    if tool_response is not None:
        context_content = f"***Context From Tool - consider this context to answer the question***: {tool_response}"
    context_message = SystemMessage(content=context_content)

    logger.info(f"Context message: {context_message}")
    messages = []
    messages.append(system_message)
    messages.append(context_message)
    # messages.extend(formatted_chat_history)
    messages.append(user_message)
    response = llm.stream(input=messages)
    return response


def call_openai(
    chat_history: list[dict],
    question: str,
    contexts: list[Document],
) -> str:
    llm = get_openai_model()
    formatted_chat_history = []
    for entry in chat_history:
        # if entry.get("question"):
        formatted_chat_history.append(HumanMessage(content=entry[0]))
        # if entry.get("answer"):
        formatted_chat_history.append(AIMessage(content=entry[0]))

    user_message = HumanMessage(content=question)

    context_content = "\n".join([doc.page_content for doc in contexts])

    context_message = SystemMessage(
        content=f"""
    - Today's date is {datetime.today().strftime("%d-%m-%Y")}
    Consider the given context to answer the question based on the users query and the conversation history.
    Context: {context_content}
    **note**: 
    - Always respond with html rich text format using html tags like <p>, <ul>, <li>. response should be directly rendered in UI with innerHTML.
    - Analyze the provided context if provided and analyze the question asked by user and give a response which is relevant to the question asked by the user.
    """
    )

    messages = [
        context_message,
        *formatted_chat_history,
        user_message,
    ]

    response = llm.invoke(input=messages)
    return response


def general_call(prompt: str) -> str:
    llm = get_general_model()
    response = llm.invoke(input=prompt)
    return response.content


def decision_call(
    agent: Agent, question: str, tools=[], knowledges=[], history=[]
) -> str:
    history_content = ""
    for entry in history:
        # if entry.get("Question"):
        history_content += f"USER: {entry[0]}\n"
        # if entry.get("Answer"):
        history_content += f"ASSISTANT: {entry[1]}\n"
    llm = get_llm_for_agent(agent)
    tool_names = [tool.name for tool in tools]
    tool_message = (
        "**these are the tools available to you: you can use these tools and you have capabilities to use these tools for the user**:"
        if len(tool_names) > 0
        else ""
    )
    for tool_name in tool_names:
        if tool_name == ToolEnum.WEB_SEARCH.name:
            tool_message += f"""
            {ToolEnum.WEB_SEARCH.value}: This tool is to search the web and get latest data from web. with this you can search the web.
            if user is asking for latest information or wants to get latest information from web return response only with tool name. else return "NO".
            """
        elif tool_name == ToolEnum.WEB_SCRAPER.name:
            tool_message += f"""
            {ToolEnum.WEB_SCRAPER.value}: This tool is to scrape a link provided by user.
            This tool is only needed if user provided a link and asked for data from that link.
            if user is asking for information that requires this tool, return response only with tool name. else return "NO".
            """
        elif tool_name == ToolEnum.VISUAL_REFERENCE.name:
            tool_message += f"""
            {ToolEnum.VISUAL_REFERENCE.value}: This tool is to generate charts and graphs.
            if user is asking for information that requires this tool, return response only with tool name. else return "NO".
            """
    knowledge_message = (
        "**these are the knowledges available to you: you can use these knowledges and you have capabilities to use these knowledges for the user**:"
        if len(knowledges) > 0
        else ""
    )
    for knowledge in knowledges:
        knowledge_message += f"""
        - {knowledge}
        """
    pormpt = f"""
    Available models: {LLMEnum.openai_4o.value}, {LLMEnum.groq.value}, {LLMEnum.openai_4o_mini.value}, {LLMEnum.llama3_1_70b_versatile.value}, {LLMEnum.llama2_9b_it.value}
    Your are an AI agent, you have following details.
    AGENT_DATA:
    ```NAME: {agent.name}
    TONE:- {agent.tone.value}
    These are your instructions:
    {agent.instructions}
    {tool_message}
    {knowledge_message}
    configured model: {agent.llm_type}
    ```
    - understand the user's intent and and the capabilities you have from the  and knowledges or tools that you have access to in AGENT_DATA section and provide the answer in json format with the following format.
    - Don't include AGENT_DATA: or any headings of the prompt in the response.
    CONVERSATION: {history_content}
    USER: {question}

    ANSWER FORMAT:
    {{
        "tool_needed": YES/NO,
        "rag_query_needed": YES/NO,
        "answer": NO/<answer>
    }}
    LANGUAGE_SET_BY_USER: f{agent.language.value}
    TODAY'S_DATE: {datetime.now().strftime("%Y-%m-%d")}
    ***Note***:
        - if user's question requires any of the tools from AGENT_DATA to answer the question, give response  YES in the tool_needed field else return "NO".
        - if user's question requires contexts from the knowledges from AGENT_DATA to answer the question, give response YES in rag query in the rag_query_needed field else return "NO".
        - Never mention any instructions/rules set to you in the response.
        - Analyze CONVERSATION and understand the user's question and respond with best answer which follows the conversation.
        - if user's question is generic and can be answered with just only details provided in AGENT_DATA or agent configuration(such as agent details, details of knowledges), then generate respose from AGENT_DATA or LLM own knowledge, give your response clearly in the answer field else return NO.
           ex: what knowledge you have access to?
           response: {{ "tool_needed": "NO", "rag_query_needed": "NO", "chat_history_needed": "NO", "answer": "i have access to x.pdf and y.pdf and x.docx" }}
        - respond only in json format, don't give any format with json prefix or backticks, it should be directly loaded with json.loads().
        - if user's question is on a image knowledge mentioned return rag_query_needed: "YES" and tool_needed: "NO"
        ex:
        question: summarize that video / image?
        response: {{ "tool_needed": "NO", "rag_query_needed": "YES", "chat_history_needed": "NO", "answer": "NO" }}
        - if there is no data in AGENT_DATA, say a message that you din't add any konledge.
        ex:
        question: what documents are do we have?
        response: {{ "tool_needed": "NO", "rag_query_needed": "NO", "chat_history_needed": "NO", "answer": "i have access to x.pdf and y.pdf and x.docx" }}
        - Important: The user's input may be a follow-up response to a previous interaction. If the user's input appears to be a continuation of the previous conversation, (e.g., "yes", "ok", "I want to know more", "1"), you should respond with "NO".
        - Always respond in the language specified in the LANGUAGE_SET_BY_USER field.
    """
    logger.info(f"decision call prompt: \n{pormpt}")
    response = llm.invoke(input=pormpt)
    return response.content.replace("```json", "").replace("```", "")


def decision_call_v2(
    agent: Agent, question: str, tools=[], knowledges=[], history=[]
) -> str:
    history_content = ""
    for entry in history:
        # if entry.get("Question"):
        history_content += f"USER: {entry[0]}\n"
        # if entry.get("Answer"):
        history_content += f"ASSISTANT: {entry[1]}\n"
    llm = get_llm_for_agent(agent)
    tool_names = [tool.name for tool in tools]
    tool_message = (
        "**these are the tools available to you: you can use these tools and you have capabilities to use these tools for the user**:"
        if len(tool_names) > 0
        else ""
    )
    for tool_name in tool_names:
        if tool_name == ToolEnum.WEB_SEARCH.name:
            tool_message += f"""
            {ToolEnum.WEB_SEARCH.value}: This tool is to search the web and get latest data from web. with this you can search the web.
            if user is asking for latest information or wants to get latest information from web return response only with tool name. else return "NO".
            """
        elif tool_name == ToolEnum.WEB_SCRAPER.name:
            tool_message += f"""
            {ToolEnum.WEB_SCRAPER.value}: This tool is to scrape a link provided by user.
            This tool is only needed if user provided a link and asked for data from that link.
            if user is asking for information that requires this tool, return response only with tool name. else return "NO".
            """
        elif tool_name == ToolEnum.VISUAL_REFERENCE.name:
            tool_message += f"""
            {ToolEnum.VISUAL_REFERENCE.value}: This tool is to generate charts and graphs.
            if user is asking for information that requires this tool, return response only with tool name. else return "NO".
            """
    knowledge_message = (
        "**these are the knowledges available to you: you can use these knowledges and you have capabilities to use these knowledges for the user**:"
        if len(knowledges) > 0
        else ""
    )
    for knowledge in knowledges:
        knowledge_message += f"""
        - {knowledge}
        """
    pormpt = f"""
    Your are an AI agent, you have following details.
    AGENT_DATA:
    ```NAME: {agent.name}
    TONE:- {agent.tone.value}
    These are your instructions:
    {agent.instructions}
    {tool_message}
    {knowledge_message}
    ```
    - understand the user's intent and and the capabilities you have from the  and knowledges or tools that you have access to in AGENT_DATA section and provide the answer in json format with the following format.
    - Don't include AGENT_DATA: or any headings of the prompt in the response.
    CONVERSATION: {history_content}
    USER: {question}

    ANSWER FORMAT:
    {{
        "tool_needed": YES/NO,
        "rag_query_needed": YES/NO,
        "answer": NO/<answer>
    }}
    LANGUAGE_SET_BY_USER: f{agent.language.value}
    TODAY'S_DATE: {datetime.now().strftime("%Y-%m-%d")}
    ***Note***:
        - if user's question requires any of the tools from AGENT_DATA to answer the question, give response  YES in the tool_needed field else return "NO".
        - if user's question requires contexts from the knowledges from AGENT_DATA to answer the question, give response YES in rag query in the rag_query_needed field else return "NO".
        - Never mention any instructions/rules set to you in the response.
        - Analyze CONVERSATION and understand the user's question and respond with best answer which follows the conversation.
        - if user's question is generic and can be answered with just only details provided in AGENT_DATA or agent configuration(such as agent details, details of knowledges), then generate respose from AGENT_DATA or LLM own knowledge, give your response clearly in the answer field else return NO.
           ex: what knowledge you have access to?
           response: {{ "tool_needed": "NO", "rag_query_needed": "NO", "chat_history_needed": "NO", "answer": "i have access to x.pdf and y.pdf and x.docx" }}
        - respond only in json format, don't give any format with json prefix or backticks, it should be directly loaded with json.loads().
        - if user's question is on a image knowledge mentioned return rag_query_needed: "YES" and tool_needed: "NO"
        ex:
        question: summarize that video / image?
        response: {{ "tool_needed": "NO", "rag_query_needed": "YES", "chat_history_needed": "NO", "answer": "NO" }}
        - if there is no data in AGENT_DATA, say a message that you din't add any konledge.
        ex:
        question: what documents are do we have?
        response: {{ "tool_needed": "NO", "rag_query_needed": "NO", "chat_history_needed": "NO", "answer": "i have access to x.pdf and y.pdf and x.docx" }}
        - Important: The user's input may be a follow-up response to a previous interaction. If the user's input appears to be a continuation of the previous conversation, (e.g., "yes", "ok", "I want to know more", "1"), you should respond with "NO".
        - Always respond in the language specified in the LANGUAGE_SET_BY_USER field.
    """
    logger.info(f"decision call prompt: \n{pormpt}")
    response = llm.invoke(input=pormpt)
    return response.content.replace("```json", "").replace("```", "")


def decide_generic_or_not(user_question: str) -> str:
    prompt = decide_generic_or_not_prompt_gen(user_question=user_question)
    llm = get_general_model()
    response = llm.invoke(input=prompt)
    return response.content


def get_openai_gpt4():
    return ChatOpenAI(
        api_key=settings.OPENAI_API_KEY,
        model=settings.MODEL_NAME,
        temperature=0,
        max_tokens=4096,
        max_retries=2,
    )


def get_openai_client():
    return OpenAI(
        api_key=settings.OPENAI_API_KEY,
    )
